/**
 * Icon Component
 * 
 * مكون الأيقونات مع دعم مكتبات متعددة
 */

import React from 'react';
import { ViewStyle } from 'react-native';

// Icon Libraries
import { Ionicons } from '@expo/vector-icons';
import { MaterialIcons } from '@expo/vector-icons';
import { Feather } from '@expo/vector-icons';
import { AntDesign } from '@expo/vector-icons';

export interface IconProps {
  name: string;
  size?: number;
  color?: string;
  style?: ViewStyle;
  library?: 'ionicons' | 'material' | 'feather' | 'antdesign';
}

// Icon name mapping for consistency
const iconMap: Record<string, { library: string; name: string }> = {
  // Navigation
  'chevron-left': { library: 'feather', name: 'chevron-left' },
  'chevron-right': { library: 'feather', name: 'chevron-right' },
  'chevron-up': { library: 'feather', name: 'chevron-up' },
  'chevron-down': { library: 'feather', name: 'chevron-down' },
  'arrow-left': { library: 'feather', name: 'arrow-left' },
  'arrow-right': { library: 'feather', name: 'arrow-right' },
  
  // Actions
  'plus': { library: 'feather', name: 'plus' },
  'minus': { library: 'feather', name: 'minus' },
  'edit': { library: 'feather', name: 'edit' },
  'trash': { library: 'feather', name: 'trash-2' },
  'search': { library: 'feather', name: 'search' },
  'filter': { library: 'feather', name: 'filter' },
  'refresh': { library: 'feather', name: 'refresh-cw' },
  'download': { library: 'feather', name: 'download' },
  'upload': { library: 'feather', name: 'upload' },
  'share': { library: 'feather', name: 'share' },
  'copy': { library: 'feather', name: 'copy' },
  
  // Communication
  'message-circle': { library: 'feather', name: 'message-circle' },
  'message-square': { library: 'feather', name: 'message-square' },
  'phone': { library: 'feather', name: 'phone' },
  'mail': { library: 'feather', name: 'mail' },
  'bell': { library: 'feather', name: 'bell' },
  
  // Media
  'camera': { library: 'feather', name: 'camera' },
  'image': { library: 'feather', name: 'image' },
  'video': { library: 'feather', name: 'video' },
  'mic': { library: 'feather', name: 'mic' },
  'mic-off': { library: 'feather', name: 'mic-off' },
  'volume': { library: 'feather', name: 'volume-2' },
  'play': { library: 'feather', name: 'play' },
  'pause': { library: 'feather', name: 'pause' },
  'stop': { library: 'feather', name: 'square' },
  
  // Interface
  'home': { library: 'feather', name: 'home' },
  'menu': { library: 'feather', name: 'menu' },
  'settings': { library: 'feather', name: 'settings' },
  'user': { library: 'feather', name: 'user' },
  'users': { library: 'feather', name: 'users' },
  'lock': { library: 'feather', name: 'lock' },
  'unlock': { library: 'feather', name: 'unlock' },
  'eye': { library: 'feather', name: 'eye' },
  'eye-off': { library: 'feather', name: 'eye-off' },
  
  // Status
  'check': { library: 'feather', name: 'check' },
  'x': { library: 'feather', name: 'x' },
  'alert-circle': { library: 'feather', name: 'alert-circle' },
  'alert-triangle': { library: 'feather', name: 'alert-triangle' },
  'info': { library: 'feather', name: 'info' },
  'help-circle': { library: 'feather', name: 'help-circle' },
  
  // Content
  'book': { library: 'feather', name: 'book' },
  'bookmark': { library: 'feather', name: 'bookmark' },
  'file': { library: 'feather', name: 'file' },
  'folder': { library: 'feather', name: 'folder' },
  'calendar': { library: 'feather', name: 'calendar' },
  'clock': { library: 'feather', name: 'clock' },
  'list': { library: 'feather', name: 'list' },
  'grid': { library: 'feather', name: 'grid' },
  
  // Health & Fitness
  'heart': { library: 'feather', name: 'heart' },
  'heart-pulse': { library: 'ionicons', name: 'pulse' },
  'activity': { library: 'feather', name: 'activity' },
  'droplet': { library: 'feather', name: 'droplet' },
  'thermometer': { library: 'feather', name: 'thermometer' },
  
  // Learning & AI
  'brain': { library: 'ionicons', name: 'brain' },
  'book-open': { library: 'feather', name: 'book-open' },
  'graduation-cap': { library: 'ionicons', name: 'school' },
  'target': { library: 'feather', name: 'target' },
  'trending-up': { library: 'feather', name: 'trending-up' },
  
  // Shopping
  'shopping-cart': { library: 'feather', name: 'shopping-cart' },
  'shopping-bag': { library: 'feather', name: 'shopping-bag' },
  'credit-card': { library: 'feather', name: 'credit-card' },
  'dollar-sign': { library: 'feather', name: 'dollar-sign' },
  'tag': { library: 'feather', name: 'tag' },
  'barcode-scan': { library: 'ionicons', name: 'barcode' },
  
  // Security & Privacy
  'shield': { library: 'feather', name: 'shield' },
  'shield-check': { library: 'feather', name: 'shield' },
  'key': { library: 'feather', name: 'key' },
  'database': { library: 'feather', name: 'database' },
  'server': { library: 'feather', name: 'server' },
  
  // Productivity
  'calendar-clock': { library: 'ionicons', name: 'calendar' },
  'briefcase': { library: 'feather', name: 'briefcase' },
  'clipboard': { library: 'feather', name: 'clipboard' },
  'check-square': { library: 'feather', name: 'check-square' },
  'square': { library: 'feather', name: 'square' },
  
  // Social & Emotions
  'smile': { library: 'feather', name: 'smile' },
  'frown': { library: 'feather', name: 'frown' },
  'meh': { library: 'feather', name: 'meh' },
  'thumbs-up': { library: 'feather', name: 'thumbs-up' },
  'thumbs-down': { library: 'feather', name: 'thumbs-down' },
  
  // Technology
  'wifi': { library: 'feather', name: 'wifi' },
  'wifi-off': { library: 'feather', name: 'wifi-off' },
  'bluetooth': { library: 'feather', name: 'bluetooth' },
  'battery': { library: 'feather', name: 'battery' },
  'cpu': { library: 'feather', name: 'cpu' },
  'memory': { library: 'ionicons', name: 'hardware-chip' },
  
  // Miscellaneous
  'globe': { library: 'feather', name: 'globe' },
  'map': { library: 'feather', name: 'map' },
  'map-pin': { library: 'feather', name: 'map-pin' },
  'star': { library: 'feather', name: 'star' },
  'coffee': { library: 'feather', name: 'coffee' },
  'palette': { library: 'ionicons', name: 'color-palette' },
  'cloud': { library: 'feather', name: 'cloud' },
  'sun': { library: 'feather', name: 'sun' },
  'moon': { library: 'feather', name: 'moon' },
  'zap': { library: 'feather', name: 'zap' },
};

const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = '#000',
  style,
  library,
}) => {
  // Get icon configuration
  const iconConfig = iconMap[name];
  const iconLibrary = library || iconConfig?.library || 'feather';
  const iconName = iconConfig?.name || name;

  // Render appropriate icon component
  switch (iconLibrary) {
    case 'ionicons':
      return (
        <Ionicons
          name={iconName as any}
          size={size}
          color={color}
          style={style}
        />
      );
    case 'material':
      return (
        <MaterialIcons
          name={iconName as any}
          size={size}
          color={color}
          style={style}
        />
      );
    case 'antdesign':
      return (
        <AntDesign
          name={iconName as any}
          size={size}
          color={color}
          style={style}
        />
      );
    case 'feather':
    default:
      return (
        <Feather
          name={iconName as any}
          size={size}
          color={color}
          style={style}
        />
      );
  }
};

export default Icon;
