#!/bin/bash

# Setup Certificates Script for LifeAI Assistant
# This script helps set up signing certificates and keystores

set -e

echo "🔐 Setting up certificates and keystores for LifeAI Assistant..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="LifeAI Assistant"
BUNDLE_ID="com.lifeai.assistant"
KEYSTORE_NAME="release.keystore"
KEY_ALIAS="lifeai-release-key"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command_exists keytool; then
        print_error "keytool not found. Please install Java JDK."
        exit 1
    fi
    
    if ! command_exists openssl; then
        print_error "openssl not found. Please install OpenSSL."
        exit 1
    fi
    
    print_success "All requirements met."
}

# Generate Android keystore
generate_android_keystore() {
    print_status "Generating Android keystore..."
    
    ANDROID_DIR="android/app"
    mkdir -p "$ANDROID_DIR"
    
    if [ -f "$ANDROID_DIR/$KEYSTORE_NAME" ]; then
        print_warning "Keystore already exists at $ANDROID_DIR/$KEYSTORE_NAME"
        read -p "Do you want to overwrite it? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Skipping keystore generation."
            return
        fi
        rm "$ANDROID_DIR/$KEYSTORE_NAME"
    fi
    
    print_status "Please provide the following information for the keystore:"
    read -p "Keystore password: " -s KEYSTORE_PASSWORD
    echo
    read -p "Key password: " -s KEY_PASSWORD
    echo
    read -p "Your name: " DEVELOPER_NAME
    read -p "Organization: " ORGANIZATION
    read -p "City: " CITY
    read -p "State/Province: " STATE
    read -p "Country code (2 letters): " COUNTRY
    
    # Generate keystore
    keytool -genkeypair \
        -v \
        -keystore "$ANDROID_DIR/$KEYSTORE_NAME" \
        -alias "$KEY_ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -validity 10000 \
        -storepass "$KEYSTORE_PASSWORD" \
        -keypass "$KEY_PASSWORD" \
        -dname "CN=$DEVELOPER_NAME, OU=$ORGANIZATION, O=$ORGANIZATION, L=$CITY, S=$STATE, C=$COUNTRY"
    
    print_success "Android keystore generated successfully!"
    
    # Create gradle.properties file
    GRADLE_PROPS="android/gradle.properties"
    print_status "Creating gradle.properties file..."
    
    cat > "$GRADLE_PROPS" << EOF
# Android keystore configuration
MYAPP_UPLOAD_STORE_FILE=$KEYSTORE_NAME
MYAPP_UPLOAD_KEY_ALIAS=$KEY_ALIAS
MYAPP_UPLOAD_STORE_PASSWORD=$KEYSTORE_PASSWORD
MYAPP_UPLOAD_KEY_PASSWORD=$KEY_PASSWORD

# Other Android settings
android.useAndroidX=true
android.enableJetifier=true
org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.daemon=true
EOF
    
    print_success "gradle.properties created successfully!"
    
    # Create environment variables template
    cat > ".env.android" << EOF
# Android signing configuration
ANDROID_KEYSTORE_PASSWORD=$KEYSTORE_PASSWORD
ANDROID_KEY_ALIAS=$KEY_ALIAS
ANDROID_KEY_PASSWORD=$KEY_PASSWORD
ANDROID_KEYSTORE_BASE64=$(base64 -i "$ANDROID_DIR/$KEYSTORE_NAME" | tr -d '\n')
EOF
    
    print_success "Environment variables template created at .env.android"
    print_warning "Remember to add .env.android to your .gitignore file!"
}

# Generate iOS certificates info
generate_ios_certificates_info() {
    print_status "Setting up iOS certificates information..."
    
    mkdir -p ios/certificates
    
    cat > "ios/certificates/README.md" << EOF
# iOS Certificates Setup

## Required Certificates

### Development
1. iOS Development Certificate
2. Development Provisioning Profile

### Distribution
1. iOS Distribution Certificate
2. App Store Provisioning Profile

## Setup Instructions

### Using Fastlane Match (Recommended)
\`\`\`bash
# Install fastlane
gem install fastlane

# Initialize match
fastlane match init

# Generate development certificates
fastlane match development

# Generate distribution certificates
fastlane match appstore
\`\`\`

### Manual Setup
1. Go to Apple Developer Portal
2. Create certificates and provisioning profiles
3. Download and install in Xcode
4. Update project settings

## Bundle Identifier
$BUNDLE_ID

## Team ID
Update in Xcode project settings and fastlane/Appfile

## App Store Connect
1. Create app in App Store Connect
2. Set up app information
3. Upload screenshots and metadata
4. Submit for review
EOF
    
    print_success "iOS certificates information created!"
}

# Generate security configuration
generate_security_config() {
    print_status "Generating security configuration..."
    
    mkdir -p config/security
    
    cat > "config/security/README.md" << EOF
# Security Configuration

## Environment Variables

### Required for CI/CD
\`\`\`bash
# Expo
EXPO_TOKEN=your_expo_token

# iOS
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=your_app_specific_password
FASTLANE_SESSION=your_fastlane_session
MATCH_PASSWORD=your_match_password

# Android
ANDROID_KEYSTORE_PASSWORD=your_keystore_password
ANDROID_KEY_ALIAS=$KEY_ALIAS
ANDROID_KEY_PASSWORD=your_key_password
ANDROID_KEYSTORE_BASE64=base64_encoded_keystore

# Google Play
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON=service_account_json_content

# Optional
SLACK_URL=your_slack_webhook_url
\`\`\`

## Security Best Practices

1. **Never commit secrets to git**
2. **Use environment variables for sensitive data**
3. **Rotate keys and passwords regularly**
4. **Use different keys for debug and release**
5. **Enable two-factor authentication**
6. **Use Fastlane Match for iOS certificates**

## Files to Keep Secure

- \`android/app/release.keystore\`
- \`android/service-account-key.json\`
- \`ios/certificates/*\`
- \`.env.*\` files
- \`fastlane/.env\`

## Backup Strategy

1. Store keystores in secure cloud storage
2. Keep encrypted backups of certificates
3. Document recovery procedures
4. Test restore procedures regularly
EOF
    
    print_success "Security configuration documentation created!"
}

# Create .gitignore entries
update_gitignore() {
    print_status "Updating .gitignore..."
    
    GITIGNORE_ENTRIES="
# Security and certificates
*.keystore
*.p12
*.mobileprovision
service-account-key.json
google-services.json
GoogleService-Info.plist

# Environment variables
.env
.env.*
!.env.example

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/.env

# iOS certificates
ios/certificates/
*.cer
*.p8

# Android signing
android/app/release.keystore
android/gradle.properties
android/keystore.properties

# Expo
.expo/
dist/
web-build/

# Temporary files
*.tmp
*.temp
"
    
    if [ -f ".gitignore" ]; then
        echo "$GITIGNORE_ENTRIES" >> .gitignore
        print_success ".gitignore updated with security entries!"
    else
        echo "$GITIGNORE_ENTRIES" > .gitignore
        print_success ".gitignore created with security entries!"
    fi
}

# Main execution
main() {
    echo "🚀 Starting certificate setup for $APP_NAME"
    echo "Bundle ID: $BUNDLE_ID"
    echo
    
    check_requirements
    
    echo
    print_status "What would you like to set up?"
    echo "1) Android keystore"
    echo "2) iOS certificates info"
    echo "3) Security configuration"
    echo "4) Update .gitignore"
    echo "5) All of the above"
    echo
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            generate_android_keystore
            ;;
        2)
            generate_ios_certificates_info
            ;;
        3)
            generate_security_config
            ;;
        4)
            update_gitignore
            ;;
        5)
            generate_android_keystore
            generate_ios_certificates_info
            generate_security_config
            update_gitignore
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    echo
    print_success "Certificate setup completed!"
    echo
    print_warning "Next steps:"
    echo "1. Add sensitive files to .gitignore"
    echo "2. Set up environment variables for CI/CD"
    echo "3. Configure Fastlane for iOS certificates"
    echo "4. Test the build process"
    echo "5. Set up Google Play Console and App Store Connect"
}

# Run main function
main "$@"
