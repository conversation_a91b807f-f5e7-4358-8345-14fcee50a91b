{"title": "Privacy Settings", "loading": "Loading privacy settings...", "saving": "Saving...", "score": {"title": "Privacy Score", "excellent": "Excellent - Your privacy is fully protected", "good": "Good - High privacy level", "fair": "Fair - Privacy can be improved", "poor": "Poor - Your privacy is at risk"}, "safeMode": {"title": "Safe Mode", "activeDescription": "Safe mode is active - all data stays local", "inactiveDescription": "Safe mode is inactive - data may be shared externally"}, "sections": {"dataCollection": "Data Collection", "aiProcessing": "AI Processing", "dataSharing": "Data Sharing", "security": "Security", "dataCategories": "Data Categories"}, "settings": {"dataCollection": "Data Collection", "dataCollectionDesc": "Allow usage data collection to improve the app", "analytics": "Analytics", "analyticsDesc": "Send analytics data to understand app usage", "crashReporting": "Crash Reporting", "crashReportingDesc": "Send crash reports to improve app stability", "sendToExternalAI": "Send to External AI", "sendToExternalAIDesc": "Send messages to external AI services", "useLocalAIOnly": "Use Local AI Only", "useLocalAIOnlyDesc": "Process all requests locally without external sending", "shareConversationHistory": "Share Conversation History", "shareConversationHistoryDesc": "Share conversation history to improve responses", "shareWithThirdParties": "Share with Third Parties", "shareWithThirdPartiesDesc": "Share data with partners and external services", "allowTelemetry": "Allow Telemetry", "allowTelemetryDesc": "Send telemetry data to improve service", "encryptLocalData": "Encrypt Local Data", "encryptLocalDataDesc": "Encrypt all data stored on device", "biometricAuth": "Biometric Authentication", "biometricAuthDesc": "Use fingerprint or face recognition for authentication", "autoLock": "Auto Lock", "autoLockDesc": "Automatically lock app after period of inactivity", "requiresRestart": "Requires app restart"}, "categories": {"sensitive": "Sensitive", "localOnly": "Local Only", "canShare": "Can Share"}, "actions": {"export": "Export Settings", "reset": "Reset to De<PERSON>ult"}, "export": {"title": "Export Privacy Settings", "success": "Settings exported successfully"}, "reset": {"title": "Reset Privacy Settings", "confirmation": "Are you sure you want to reset all privacy settings to default values?", "confirm": "Reset"}, "warnings": {"title": "Security Warning", "disableEncryption": "Disabling encryption may put your data at risk. Are you sure?"}, "errors": {"loadFailed": "Failed to load privacy settings", "updateFailed": "Failed to update privacy settings", "exportFailed": "Failed to export settings", "resetFailed": "Failed to reset settings", "safeModeToggleFailed": "Failed to toggle safe mode"}}