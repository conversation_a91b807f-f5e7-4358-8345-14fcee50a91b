/**
 * Card Component
 * 
 * بطاقة أساسية مع ظل وحواف مدورة
 */

import React from 'react';
import { View, ViewStyle, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { useTheme } from '@/ui/theme/useTheme';
import { DESIGN_TOKENS } from '../index';

export interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  padding?: keyof typeof DESIGN_TOKENS.spacing;
  margin?: keyof typeof DESIGN_TOKENS.spacing;
  radius?: keyof typeof DESIGN_TOKENS.radius;
  shadow?: keyof typeof DESIGN_TOKENS.shadow;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  style?: ViewStyle;
  pressable?: boolean;
  onPress?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  padding = 'md',
  margin,
  radius = 'lg',
  shadow = 'md',
  backgroundColor,
  borderColor,
  borderWidth,
  style,
  pressable = false,
  onPress,
  ...props
}) => {
  const theme = useTheme();

  const cardStyle: ViewStyle = {
    backgroundColor: backgroundColor || theme.colors.surface,
    padding: DESIGN_TOKENS.spacing[padding],
    margin: margin ? DESIGN_TOKENS.spacing[margin] : undefined,
    borderRadius: DESIGN_TOKENS.radius[radius],
    borderColor: borderColor || theme.colors.border,
    borderWidth: borderWidth || (borderColor ? 1 : 0),
    ...DESIGN_TOKENS.shadow[shadow],
  };

  if (pressable || onPress) {
    return (
      <TouchableOpacity
        style={[cardStyle, style]}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
};

export default Card;
