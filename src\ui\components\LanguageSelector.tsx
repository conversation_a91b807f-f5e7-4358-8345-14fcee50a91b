/**
 * Language Selector Component
 * 
 * Quick language selection component for headers and settings
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Pressable,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { 
  SUPPORTED_LANGUAGES, 
  SupportedLanguage, 
  getCurrentLanguageInfo 
} from '@/locales/i18n';
import { useLanguage, useRTLStyles } from '@/locales/hooks/useLanguage';

interface LanguageSelectorProps {
  style?: any;
  showLabel?: boolean;
  compact?: boolean;
  onLanguageChange?: (language: SupportedLanguage) => void;
}

interface LanguageItem {
  code: SupportedLanguage;
  name: string;
  nameEn: string;
  flag: string;
  rtl: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  style,
  showLabel = true,
  compact = false,
  onLanguageChange,
}) => {
  const { t } = useTranslation(['common']);
  const { currentLanguage, changeLanguage, isChangingLanguage } = useLanguage();
  const { isRTL, textAlign, flexDirection } = useRTLStyles();

  // State
  const [modalVisible, setModalVisible] = useState(false);

  const currentLanguageInfo = getCurrentLanguageInfo();

  const handleLanguageSelect = async (language: SupportedLanguage) => {
    if (language === currentLanguage) {
      setModalVisible(false);
      return;
    }

    try {
      await changeLanguage(language);
      onLanguageChange?.(language);
      setModalVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const renderLanguageItem = ({ item }: { item: LanguageItem }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        item.code === currentLanguage && styles.selectedLanguageItem,
        { flexDirection },
      ]}
      onPress={() => handleLanguageSelect(item.code)}
    >
      <Text style={styles.languageFlag}>{item.flag}</Text>
      <View style={styles.languageInfo}>
        <Text style={[
          styles.languageName,
          item.code === currentLanguage && styles.selectedLanguageName,
          { textAlign },
        ]}>
          {item.name}
        </Text>
        <Text style={[
          styles.languageNameEn,
          item.code === currentLanguage && styles.selectedLanguageNameEn,
          { textAlign },
        ]}>
          {item.nameEn}
        </Text>
      </View>
      {item.code === currentLanguage && (
        <Icon name="check" size={20} color="#4CAF50" />
      )}
    </TouchableOpacity>
  );

  const renderCompactSelector = () => (
    <TouchableOpacity
      style={[styles.compactSelector, style]}
      onPress={() => setModalVisible(true)}
      disabled={isChangingLanguage}
    >
      <Text style={styles.compactFlag}>{currentLanguageInfo.flag}</Text>
      <Icon 
        name="keyboard-arrow-down" 
        size={16} 
        color="#666" 
        style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }}
      />
    </TouchableOpacity>
  );

  const renderFullSelector = () => (
    <TouchableOpacity
      style={[styles.fullSelector, style, { flexDirection }]}
      onPress={() => setModalVisible(true)}
      disabled={isChangingLanguage}
    >
      <Text style={styles.flag}>{currentLanguageInfo.flag}</Text>
      <View style={styles.languageTextContainer}>
        {showLabel && (
          <Text style={[styles.label, { textAlign }]}>
            {t('languages.current')}
          </Text>
        )}
        <Text style={[styles.currentLanguageName, { textAlign }]}>
          {currentLanguageInfo.name}
        </Text>
      </View>
      <Icon 
        name="keyboard-arrow-down" 
        size={20} 
        color="#666"
        style={{ transform: [{ scaleX: isRTL ? -1 : 1 }] }}
      />
    </TouchableOpacity>
  );

  return (
    <>
      {compact ? renderCompactSelector() : renderFullSelector()}

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}
        >
          <View style={[styles.modalContent, { direction: isRTL ? 'rtl' : 'ltr' }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { textAlign }]}>
                {t('languages.select')}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={SUPPORTED_LANGUAGES}
              keyExtractor={(item) => item.code}
              renderItem={renderLanguageItem}
              style={styles.languageList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  // Compact selector
  compactSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#f5f5f5',
    minWidth: 50,
  },
  compactFlag: {
    fontSize: 16,
    marginRight: 4,
  },

  // Full selector
  fullSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  flag: {
    fontSize: 20,
    marginRight: 12,
  },
  languageTextContainer: {
    flex: 1,
  },
  label: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  currentLanguageName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },

  // Modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '80%',
    maxWidth: 400,
    maxHeight: '70%',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  closeButton: {
    padding: 4,
  },

  // Language list
  languageList: {
    maxHeight: 300,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedLanguageItem: {
    backgroundColor: '#f1f8e9',
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  selectedLanguageName: {
    color: '#4CAF50',
  },
  languageNameEn: {
    fontSize: 14,
    color: '#666',
  },
  selectedLanguageNameEn: {
    color: '#4CAF50',
  },
});

export default LanguageSelector;
