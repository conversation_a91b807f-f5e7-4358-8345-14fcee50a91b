{"expo": {"name": "LifeAI Assistant", "slug": "<PERSON><PERSON>-assistant", "description": "AI-powered mobile app for life management", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.lifeai.assistant", "buildNumber": "1", "icon": "./assets/images/icon-ios.png", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to capture photos for AI analysis and AR features.", "NSMicrophoneUsageDescription": "This app uses the microphone for voice messages and voice commands.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to select images for AI analysis.", "NSLocationWhenInUseUsageDescription": "This app uses location to provide location-based AI assistance.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication.", "NSContactsUsageDescription": "This app accesses contacts to help with contact-related AI tasks.", "NSCalendarsUsageDescription": "This app accesses your calendar for smart planning features.", "NSRemindersUsageDescription": "This app accesses reminders for smart planning and task management.", "NSHealthShareUsageDescription": "This app accesses health data to provide personalized health insights.", "NSHealthUpdateUsageDescription": "This app updates health data based on AI recommendations.", "NSUserTrackingUsageDescription": "This app uses tracking to provide personalized AI experiences while respecting your privacy.", "ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true, "CFBundleDevelopmentRegion": "en", "CFBundleLocalizations": ["en", "ar"]}, "config": {"usesNonExemptEncryption": false}, "associatedDomains": ["applinks:lifeai.app"], "entitlements": {"com.apple.developer.applesignin": ["<PERSON><PERSON><PERSON>"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.lifeai.assistant", "versionCode": 1, "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.VIBRATE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [], "scheme": "lifeai"}}