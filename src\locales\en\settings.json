{"title": "Settings", "sections": {"general": "General", "account": "Account", "privacy": "Privacy & Security", "notifications": "Notifications", "language": "Language & Region", "appearance": "Appearance", "storage": "Storage", "about": "About", "support": "Support", "advanced": "Advanced"}, "general": {"language": {"title": "Language", "description": "Choose your preferred language", "current": "Current: {{language}}", "change": "Change Language"}, "region": {"title": "Region", "description": "Set your region for localized content", "current": "Current: {{region}}"}, "timezone": {"title": "Time Zone", "description": "Set your time zone", "current": "Current: {{timezone}}", "auto": "Automatic"}, "dateFormat": {"title": "Date Format", "description": "Choose how dates are displayed", "options": {"mdy": "MM/DD/YYYY", "dmy": "DD/MM/YYYY", "ymd": "YYYY/MM/DD"}}, "timeFormat": {"title": "Time Format", "description": "Choose 12-hour or 24-hour format", "12hour": "12-hour (AM/PM)", "24hour": "24-hour"}}, "account": {"profile": {"title": "Profile", "description": "Manage your profile information", "edit": "Edit Profile"}, "email": {"title": "Email", "description": "Change your email address", "current": "Current: {{email}}", "change": "Change Email", "verify": "<PERSON><PERSON><PERSON>"}, "password": {"title": "Password", "description": "Change your password", "change": "Change Password", "lastChanged": "Last changed: {{date}}"}, "phone": {"title": "Phone Number", "description": "Add or change your phone number", "add": "Add Phone Number", "change": "Change Phone Number", "verify": "Verify Phone Number"}, "twoFactor": {"title": "Two-Factor Authentication", "description": "Add an extra layer of security", "enable": "Enable 2FA", "disable": "Disable 2FA", "status": "Status: {{status}}"}, "sessions": {"title": "Active Sessions", "description": "Manage your active sessions", "view": "View Sessions", "terminateAll": "Terminate All Sessions"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently delete your account", "delete": "Delete Account", "warning": "This action cannot be undone"}}, "privacy": {"dataCollection": {"title": "Data Collection", "description": "Control what data is collected", "analytics": "Analytics", "crashReports": "Crash Reports", "usage": "Usage Statistics"}, "sharing": {"title": "Data Sharing", "description": "Control data sharing with third parties", "thirdParty": "Third-party Services", "marketing": "Marketing Communications", "research": "Research & Development"}, "encryption": {"title": "Encryption", "description": "Encrypt your local data", "enable": "Enable Encryption", "status": "Status: {{status}}"}, "biometric": {"title": "Biometric Authentication", "description": "Use fingerprint or face recognition", "enable": "Enable Biometric Auth", "disable": "Disable Biometric Auth", "status": "Status: {{status}}"}, "autoLock": {"title": "Auto Lock", "description": "Automatically lock the app", "enable": "Enable Auto Lock", "timeout": "Lock after {{minutes}} minutes", "options": {"immediate": "Immediately", "1min": "1 minute", "5min": "5 minutes", "15min": "15 minutes", "30min": "30 minutes", "never": "Never"}}, "safeMode": {"title": "Safe Mode", "description": "Work offline with local AI only", "enable": "Enable Safe Mode", "disable": "Disable Safe Mode", "status": "Status: {{status}}", "autoEnable": "Auto-enable when offline"}}, "notifications": {"push": {"title": "Push Notifications", "description": "Receive notifications on your device", "enable": "Enable Push Notifications"}, "email": {"title": "Email Notifications", "description": "Receive notifications via email", "enable": "Enable Email Notifications"}, "categories": {"messages": "Messages", "reminders": "Reminders", "updates": "App Updates", "security": "Security Alerts", "marketing": "Marketing", "news": "News & Tips"}, "schedule": {"title": "Notification Schedule", "description": "Set quiet hours", "quietHours": "Quiet Hours", "from": "From", "to": "To", "weekends": "Include Weekends"}, "sounds": {"title": "Notification Sounds", "description": "Choose notification sounds", "default": "<PERSON><PERSON><PERSON>", "custom": "Custom", "silent": "Silent"}}, "appearance": {"theme": {"title": "Theme", "description": "Choose your preferred theme", "light": "Light", "dark": "Dark", "auto": "Automatic"}, "colorScheme": {"title": "Color Scheme", "description": "Customize app colors", "blue": "Blue", "green": "Green", "purple": "Purple", "orange": "Orange", "red": "Red"}, "fontSize": {"title": "Font Size", "description": "Adjust text size", "small": "Small", "medium": "Medium", "large": "Large", "extraLarge": "Extra Large"}, "animations": {"title": "Animations", "description": "Enable or disable animations", "enable": "Enable Animations", "reduce": "Reduce Motion"}}, "storage": {"usage": {"title": "Storage Usage", "description": "View storage usage details", "total": "Total: {{size}}", "available": "Available: {{size}}", "used": "Used: {{size}}"}, "cache": {"title": "<PERSON><PERSON>", "description": "Manage app cache", "size": "Cache size: {{size}}", "clear": "<PERSON>ache", "cleared": "<PERSON><PERSON> cleared successfully"}, "backup": {"title": "Backup", "description": "Backup your data", "create": "Create Backup", "restore": "Restore Backup", "auto": "Automatic Backup", "lastBackup": "Last backup: {{date}}"}, "export": {"title": "Export Data", "description": "Export your data", "export": "Export Data", "format": "Format: {{format}}"}}, "about": {"version": {"title": "Version", "current": "Current: {{version}}", "checkUpdates": "Check for Updates", "upToDate": "You're up to date", "updateAvailable": "Update available"}, "build": {"title": "Build Information", "number": "Build: {{build}}", "date": "Date: {{date}}"}, "legal": {"title": "Legal", "terms": "Terms of Service", "privacy": "Privacy Policy", "licenses": "Open Source Licenses"}, "contact": {"title": "Contact", "support": "Support", "feedback": "Send Feedback", "website": "Website", "social": "Social Media"}}, "support": {"help": {"title": "Help Center", "description": "Find answers to common questions", "browse": "Browse Help Articles"}, "contact": {"title": "Contact Support", "description": "Get help from our support team", "email": "Email Support", "chat": "Live Chat"}, "feedback": {"title": "Send Feedback", "description": "Help us improve the app", "send": "Send Feedback"}, "bugs": {"title": "Report Bug", "description": "Report issues or bugs", "report": "Report Bug"}}, "advanced": {"developer": {"title": "Developer Options", "description": "Advanced settings for developers", "enable": "Enable Developer Mode"}, "debug": {"title": "Debug Mode", "description": "Enable debug logging", "enable": "Enable Debug Mode"}, "reset": {"title": "Reset Settings", "description": "Reset all settings to default", "reset": "Reset Settings", "confirm": "Are you sure you want to reset all settings?"}, "factory": {"title": "Factory Reset", "description": "Reset app to factory settings", "reset": "Factory Reset", "warning": "This will delete all your data", "confirm": "Are you sure? This cannot be undone."}}, "actions": {"save": "Save Changes", "cancel": "Cancel", "reset": "Reset", "apply": "Apply", "test": "Test", "preview": "Preview"}, "messages": {"saved": "Setting<PERSON> saved successfully", "resetConfirm": "Settings have been reset to default", "restartRequired": "Restart required for changes to take effect", "permissionRequired": "Permission required for this feature", "featureUnavailable": "This feature is not available on your device"}}