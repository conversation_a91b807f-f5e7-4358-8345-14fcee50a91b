# 🚀 دليل النشر الشامل - LifeAI Assistant Deployment Guide

## 📋 نظرة عامة

تم إعداد نظام نشر متكامل لتطبيق LifeAI Assistant يدعم النشر على App Store و Google Play باستخدام أحدث التقنيات والأدوات.

## 🛠️ الأدوات المستخدمة

### 1. **EAS (Expo Application Services)**
- بناء التطبيق في السحابة
- إدارة الشهادات والتوقيع
- نشر تلقائي للمتاجر

### 2. **Fastlane**
- أتمتة عملية النشر
- إدارة الشهادات والملفات الشخصية
- رفع البيانات الوصفية والصور

### 3. **GitHub Actions**
- CI/CD تلقائي
- اختبار وبناء ونشر
- تكامل مع EAS و Fastlane

## 📁 هيكل الملفات

```
├── app.json                    # تكوين Expo الرئيسي
├── eas.json                    # تكوين EAS Build
├── fastlane/
│   ├── Fastfile               # سكريبت Fastlane
│   ├── Appfile                # معلومات التطبيق
│   └── metadata/              # بيانات المتاجر
│       ├── en-US/             # الإنجليزية
│       ├── ar/                # العربية
│       └── android/           # Android metadata
├── .github/workflows/         # GitHub Actions
├── android/
│   ├── app/build.gradle       # تكوين Android
│   └── app/proguard-rules.pro # قواعد ProGuard
├── assets/images/             # الأيقونات والصور
└── scripts/
    ├── generate-icons.js      # توليد الأيقونات
    └── setup-certificates.sh  # إعداد الشهادات
```

## 🔧 الإعداد الأولي

### 1. تثبيت الأدوات المطلوبة

```bash
# تثبيت EAS CLI
npm install -g @expo/eas-cli

# تثبيت Fastlane
gem install fastlane

# تسجيل الدخول إلى Expo
eas login

# تسجيل الدخول إلى Apple Developer
fastlane spaceauth -u <EMAIL>
```

### 2. إعداد الشهادات

```bash
# تشغيل سكريبت إعداد الشهادات
npm run setup:certificates

# أو يدوياً
chmod +x scripts/setup-certificates.sh
./scripts/setup-certificates.sh
```

### 3. إعداد متغيرات البيئة

إنشاء ملف `.env` مع المتغيرات التالية:

```bash
# Expo
EXPO_TOKEN=your_expo_token

# iOS
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=your_app_specific_password
FASTLANE_SESSION=your_fastlane_session
MATCH_PASSWORD=your_match_password

# Android
ANDROID_KEYSTORE_PASSWORD=your_keystore_password
ANDROID_KEY_ALIAS=lifeai-release-key
ANDROID_KEY_PASSWORD=your_key_password

# Google Play
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON=service_account_json_content

# اختياري
SLACK_URL=your_slack_webhook_url
```

## 🎨 إعداد الأيقونات والصور

### 1. توليد الأيقونات

```bash
# وضع الأيقونة المصدر (1024x1024) في assets/images/icon-source.png
# وضع صورة البداية (1242x2436) في assets/images/splash-source.png

# توليد جميع الأحجام
npm run generate:icons
```

### 2. الأحجام المطلوبة

#### iOS
- App Store: 1024x1024px
- iPhone: 180x180px, 120x120px, 87x87px, 80x80px, 60x60px
- iPad: 167x167px, 152x152px, 76x76px

#### Android
- Play Store: 512x512px
- Adaptive Icon: 1024x1024px
- Densities: 48px (mdpi) إلى 192px (xxxhdpi)

## 🏗️ عملية البناء

### 1. البناء المحلي (للاختبار)

```bash
# Android
npm run android

# iOS
npm run ios

# الويب
npm run web
```

### 2. البناء باستخدام EAS

```bash
# بناء للمعاينة
npm run build:preview

# بناء للإنتاج
npm run build:production

# بناء منصة واحدة
npm run build:android
npm run build:ios
```

### 3. البناء باستخدام Fastlane

```bash
# Android beta
npm run deploy:android

# iOS beta
npm run deploy:ios

# الإنتاج (كلا المنصتين)
npm run deploy:production
```

## 📱 النشر على المتاجر

### App Store (iOS)

#### 1. الإعداد الأولي
```bash
# إعداد الشهادات باستخدام Match
cd ios
fastlane match init
fastlane match development
fastlane match appstore
```

#### 2. النشر للاختبار (TestFlight)
```bash
cd ios
fastlane beta
```

#### 3. النشر للإنتاج
```bash
cd ios
fastlane release
```

### Google Play (Android)

#### 1. الإعداد الأولي
- إنشاء حساب Google Play Console
- رفع service account key
- إعداد keystore للتوقيع

#### 2. النشر للاختبار الداخلي
```bash
cd android
fastlane beta
```

#### 3. النشر للإنتاج
```bash
cd android
fastlane release
```

## 🔄 CI/CD التلقائي

### GitHub Actions

تم إعداد workflows تلقائية:

#### 1. عند Push إلى main
- تشغيل الاختبارات
- بناء نسخة beta
- نشر على TestFlight و Google Play Internal

#### 2. عند إنشاء Tag
- بناء نسخة الإنتاج
- نشر على App Store و Google Play
- إنشاء GitHub Release

#### 3. عند Pull Request
- تشغيل الاختبارات
- بناء نسخة معاينة

### متغيرات GitHub Secrets

إضافة المتغيرات التالية في GitHub Repository Settings:

```
EXPO_TOKEN
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD
FASTLANE_SESSION
MATCH_PASSWORD
ANDROID_KEYSTORE_PASSWORD
ANDROID_KEY_ALIAS
ANDROID_KEY_PASSWORD
ANDROID_KEYSTORE_BASE64
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON
SLACK_URL (اختياري)
```

## 📊 مراقبة النشر

### 1. Expo Dashboard
- مراقبة حالة البناء
- تحميل الملفات المبنية
- إدارة التحديثات OTA

### 2. App Store Connect
- مراقبة حالة المراجعة
- إدارة البيانات الوصفية
- تحليل الأداء

### 3. Google Play Console
- مراقبة حالة النشر
- إدارة الإصدارات
- تحليل المستخدمين

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الشهادات (iOS)
```bash
# إعادة إنشاء الشهادات
fastlane match nuke development
fastlane match nuke distribution
fastlane match development
fastlane match appstore
```

#### 2. خطأ في Keystore (Android)
```bash
# إعادة إنشاء keystore
./scripts/setup-certificates.sh
```

#### 3. خطأ في البناء
```bash
# تنظيف وإعادة البناء
npm run clean
npm install
npm run prebuild:clean
```

## 📋 قائمة مراجعة النشر

### قبل النشر
- [ ] اختبار التطبيق على الأجهزة الحقيقية
- [ ] مراجعة البيانات الوصفية والوصف
- [ ] التأكد من الأيقونات وصور البداية
- [ ] اختبار الترجمة (عربي/إنجليزي)
- [ ] مراجعة الأذونات المطلوبة
- [ ] اختبار الميزات الحساسة (كاميرا، موقع، إلخ)

### بعد النشر
- [ ] مراقبة التقييمات والمراجعات
- [ ] متابعة تحليلات الاستخدام
- [ ] الرد على ملاحظات المستخدمين
- [ ] تحديث الوثائق والدعم

## 🔄 تحديث الإصدارات

### 1. تحديث رقم الإصدار
```bash
# في app.json
"version": "1.1.0"

# Android (تلقائي مع EAS)
"versionCode": 2

# iOS (تلقائي مع EAS)
"buildNumber": "2"
```

### 2. إنشاء Tag للإصدار
```bash
git tag v1.1.0
git push origin v1.1.0
```

### 3. النشر التلقائي
سيتم تشغيل GitHub Actions تلقائياً لبناء ونشر الإصدار الجديد.

## 📞 الدعم والمساعدة

### الموارد المفيدة
- [Expo Documentation](https://docs.expo.dev/)
- [Fastlane Documentation](https://docs.fastlane.tools/)
- [App Store Guidelines](https://developer.apple.com/app-store/guidelines/)
- [Google Play Policies](https://play.google.com/about/developer-content-policy/)

### الحصول على المساعدة
- Expo Discord Community
- Fastlane GitHub Issues
- Stack Overflow
- React Native Community

---

## 🎉 تهانينا!

تم إعداد نظام نشر متكامل وجاهز للاستخدام. يمكنك الآن نشر تطبيق LifeAI Assistant على App Store و Google Play بسهولة وثقة! 🚀
