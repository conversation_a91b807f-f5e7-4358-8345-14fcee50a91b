/**
 * Container Component
 * 
 * حاوي أساسي للشاشات مع padding وتخطيط متسق
 */

import React from 'react';
import { View, ViewStyle, StyleSheet } from 'react-native';
import { useTheme } from '@/ui/theme/useTheme';
import { DESIGN_TOKENS } from '../index';

export interface ContainerProps {
  children: React.ReactNode;
  padding?: keyof typeof DESIGN_TOKENS.spacing;
  paddingHorizontal?: keyof typeof DESIGN_TOKENS.spacing;
  paddingVertical?: keyof typeof DESIGN_TOKENS.spacing;
  backgroundColor?: string;
  style?: ViewStyle;
  safe?: boolean;
  center?: boolean;
  flex?: boolean;
}

const Container: React.FC<ContainerProps> = ({
  children,
  padding = 'md',
  paddingHorizontal,
  paddingVertical,
  backgroundColor,
  style,
  safe = true,
  center = false,
  flex = true,
}) => {
  const theme = useTheme();

  const containerStyle: ViewStyle = {
    flex: flex ? 1 : undefined,
    backgroundColor: backgroundColor || theme.colors.background,
    padding: DESIGN_TOKENS.spacing[padding],
    paddingHorizontal: paddingHorizontal ? DESIGN_TOKENS.spacing[paddingHorizontal] : undefined,
    paddingVertical: paddingVertical ? DESIGN_TOKENS.spacing[paddingVertical] : undefined,
    paddingTop: safe ? theme.spacing.safeAreaTop : undefined,
    justifyContent: center ? 'center' : undefined,
    alignItems: center ? 'center' : undefined,
  };

  return (
    <View style={[containerStyle, style]}>
      {children}
    </View>
  );
};

export default Container;
