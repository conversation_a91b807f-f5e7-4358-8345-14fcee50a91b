{"expo": {"name": "LifeAI Assistant", "slug": "<PERSON><PERSON>-assistant", "description": "AI-powered mobile app for life management with privacy-first approach", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.lifeai.assistant", "buildNumber": "1", "icon": "./assets/images/icon-ios.png", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to capture photos for AI analysis and AR features.", "NSMicrophoneUsageDescription": "This app uses the microphone for voice messages and voice commands.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to select images for AI analysis.", "NSLocationWhenInUseUsageDescription": "This app uses location to provide location-based AI assistance.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication.", "NSContactsUsageDescription": "This app accesses contacts to help with contact-related AI tasks.", "NSCalendarsUsageDescription": "This app accesses your calendar for smart planning features.", "NSRemindersUsageDescription": "This app accesses reminders for smart planning and task management.", "NSHealthShareUsageDescription": "This app accesses health data to provide personalized health insights.", "NSHealthUpdateUsageDescription": "This app updates health data based on AI recommendations.", "NSUserTrackingUsageDescription": "This app uses tracking to provide personalized AI experiences while respecting your privacy.", "ITSAppUsesNonExemptEncryption": false, "CFBundleAllowMixedLocalizations": true, "CFBundleDevelopmentRegion": "en", "CFBundleLocalizations": ["en", "ar"]}, "config": {"usesNonExemptEncryption": false}, "associatedDomains": ["applinks:lifeai.app"], "entitlements": {"com.apple.developer.applesignin": ["<PERSON><PERSON><PERSON>"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.lifeai.assistant", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.USE_FINGERPRINT", "android.permission.USE_BIOMETRIC", "android.permission.READ_CONTACTS", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.VIBRATE", "android.permission.WAKE_LOCK", "android.permission.RECEIVE_BOOT_COMPLETED"], "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "lifeai.app"}], "category": ["BROWSABLE", "DEFAULT"]}], "googleServicesFile": "./google-services.json"}, "web": {"favicon": "./assets/images/favicon.png", "bundler": "metro"}, "plugins": ["expo-localization", "expo-secure-store", "expo-camera", "expo-av", "expo-location", "expo-contacts", "expo-calendar", "expo-notifications", ["expo-build-properties", {"android": {"enableProguardInReleaseBuilds": true, "enableShrinkResourcesInReleaseBuilds": true, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21}, "ios": {"deploymentTarget": "13.0"}}], ["expo-font", {"fonts": ["./assets/fonts/Cairo-Regular.ttf", "./assets/fonts/Cairo-Bold.ttf", "./assets/fonts/Roboto-Regular.ttf", "./assets/fonts/Roboto-Bold.ttf"]}], ["expo-local-authentication", {"faceIDPermission": "Allow LifeAI Assistant to use Face ID for secure authentication."}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "your-eas-project-id"}}, "owner": "lifeai-team", "privacy": "public", "scheme": "lifeai", "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/your-eas-project-id"}, "runtimeVersion": {"policy": "sdkVersion"}}}