/**
 * Text Component
 * 
 * مكون نص أساسي مع أنماط متعددة ودعم الترجمة
 */

import React from 'react';
import { Text as RNText, TextStyle, TextProps as RNTextProps } from 'react-native';
import { useTheme } from '@/ui/theme/useTheme';
import { useTranslation } from 'react-i18next';
import { DESIGN_TOKENS } from '../index';

export interface TextProps extends RNTextProps {
  children?: React.ReactNode;
  variant?: 'body1' | 'body2' | 'caption' | 'overline' | 'subtitle1' | 'subtitle2';
  color?: 'primary' | 'secondary' | 'text' | 'textSecondary' | 'error' | 'warning' | 'success';
  weight?: keyof typeof DESIGN_TOKENS.fontWeight;
  align?: 'left' | 'center' | 'right' | 'justify';
  transform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
  lineHeight?: keyof typeof DESIGN_TOKENS.lineHeight;
  i18nKey?: string;
  i18nOptions?: any;
  style?: TextStyle;
}

const Text: React.FC<TextProps> = ({
  children,
  variant = 'body1',
  color = 'text',
  weight = 'normal',
  align = 'left',
  transform = 'none',
  lineHeight = 'normal',
  i18nKey,
  i18nOptions,
  style,
  ...props
}) => {
  const theme = useTheme();
  const { t } = useTranslation();

  const variantStyles = {
    body1: {
      fontSize: DESIGN_TOKENS.fontSize.md,
      lineHeight: DESIGN_TOKENS.fontSize.md * DESIGN_TOKENS.lineHeight.normal,
    },
    body2: {
      fontSize: DESIGN_TOKENS.fontSize.sm,
      lineHeight: DESIGN_TOKENS.fontSize.sm * DESIGN_TOKENS.lineHeight.normal,
    },
    caption: {
      fontSize: DESIGN_TOKENS.fontSize.xs,
      lineHeight: DESIGN_TOKENS.fontSize.xs * DESIGN_TOKENS.lineHeight.normal,
    },
    overline: {
      fontSize: DESIGN_TOKENS.fontSize.xs,
      lineHeight: DESIGN_TOKENS.fontSize.xs * DESIGN_TOKENS.lineHeight.normal,
      textTransform: 'uppercase' as const,
      letterSpacing: 1,
    },
    subtitle1: {
      fontSize: DESIGN_TOKENS.fontSize.lg,
      lineHeight: DESIGN_TOKENS.fontSize.lg * DESIGN_TOKENS.lineHeight.normal,
      fontWeight: DESIGN_TOKENS.fontWeight.medium,
    },
    subtitle2: {
      fontSize: DESIGN_TOKENS.fontSize.md,
      lineHeight: DESIGN_TOKENS.fontSize.md * DESIGN_TOKENS.lineHeight.normal,
      fontWeight: DESIGN_TOKENS.fontWeight.medium,
    },
  };

  const colorMap = {
    primary: theme.colors.primary.main,
    secondary: theme.colors.secondary.main,
    text: theme.colors.text,
    textSecondary: theme.colors.textSecondary,
    error: theme.colors.error.main,
    warning: theme.colors.warning.main,
    success: theme.colors.success.main,
  };

  const textStyle: TextStyle = {
    ...variantStyles[variant],
    color: colorMap[color],
    fontWeight: DESIGN_TOKENS.fontWeight[weight],
    textAlign: align,
    textTransform: transform,
    lineHeight: variant === 'overline' 
      ? variantStyles[variant].lineHeight 
      : variantStyles[variant].fontSize * DESIGN_TOKENS.lineHeight[lineHeight],
    writingDirection: theme.isRTL ? 'rtl' : 'ltr',
  };

  const content = i18nKey ? t(i18nKey, i18nOptions) : children;

  return (
    <RNText style={[textStyle, style]} {...props}>
      {content}
    </RNText>
  );
};

export default Text;
