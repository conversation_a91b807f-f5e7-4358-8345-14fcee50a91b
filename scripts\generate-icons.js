#!/usr/bin/env node

/**
 * Icon Generation Script
 * 
 * Generates all required app icons from a source image
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Icon sizes for different platforms
const iconSizes = {
  ios: [
    { size: 20, name: 'icon-20.png' },
    { size: 29, name: 'icon-29.png' },
    { size: 40, name: 'icon-40.png' },
    { size: 58, name: 'icon-58.png' },
    { size: 60, name: 'icon-60.png' },
    { size: 76, name: 'icon-76.png' },
    { size: 80, name: 'icon-80.png' },
    { size: 87, name: 'icon-87.png' },
    { size: 120, name: 'icon-120.png' },
    { size: 152, name: 'icon-152.png' },
    { size: 167, name: 'icon-167.png' },
    { size: 180, name: 'icon-180.png' },
    { size: 1024, name: 'icon-ios.png' }
  ],
  android: [
    { size: 48, name: 'icon-48.png' },
    { size: 72, name: 'icon-72.png' },
    { size: 96, name: 'icon-96.png' },
    { size: 144, name: 'icon-144.png' },
    { size: 192, name: 'icon-192.png' },
    { size: 1024, name: 'icon.png' },
    { size: 1024, name: 'adaptive-icon.png' }
  ],
  web: [
    { size: 32, name: 'favicon.png' },
    { size: 512, name: 'logo.png' }
  ]
};

// Splash screen sizes
const splashSizes = [
  { width: 1242, height: 2436, name: 'splash.png' },
  { width: 1242, height: 2436, name: 'splash-ios.png' },
  { width: 1242, height: 2436, name: 'splash-android.png' }
];

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }
}

function generateIcon(sourcePath, outputPath, size) {
  try {
    const command = `convert "${sourcePath}" -resize ${size}x${size} "${outputPath}"`;
    execSync(command, { stdio: 'pipe' });
    console.log(`✅ Generated: ${outputPath} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate ${outputPath}:`, error.message);
  }
}

function generateSplashScreen(sourcePath, outputPath, width, height) {
  try {
    const command = `convert "${sourcePath}" -resize ${width}x${height} -gravity center -background white -extent ${width}x${height} "${outputPath}"`;
    execSync(command, { stdio: 'pipe' });
    console.log(`✅ Generated splash: ${outputPath} (${width}x${height})`);
  } catch (error) {
    console.error(`❌ Failed to generate splash ${outputPath}:`, error.message);
  }
}

function createPlaceholderIcon(outputPath, size, text) {
  try {
    const command = `convert -size ${size}x${size} xc:"#2196F3" -gravity center -pointsize ${Math.floor(size/8)} -fill white -annotate +0+0 "${text}" "${outputPath}"`;
    execSync(command, { stdio: 'pipe' });
    console.log(`📱 Created placeholder: ${outputPath} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to create placeholder ${outputPath}:`, error.message);
  }
}

function createPlaceholderSplash(outputPath, width, height) {
  try {
    const logoSize = Math.min(width, height) / 4;
    const command = `convert -size ${width}x${height} xc:white -gravity center -pointsize ${Math.floor(logoSize/8)} -fill "#2196F3" -annotate +0-${logoSize/4} "LifeAI" -pointsize ${Math.floor(logoSize/12)} -fill "#666" -annotate +0+${logoSize/4} "Assistant" "${outputPath}"`;
    execSync(command, { stdio: 'pipe' });
    console.log(`🖼️ Created placeholder splash: ${outputPath} (${width}x${height})`);
  } catch (error) {
    console.error(`❌ Failed to create placeholder splash ${outputPath}:`, error.message);
  }
}

function checkImageMagick() {
  try {
    execSync('convert -version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    console.error('❌ ImageMagick not found. Please install ImageMagick:');
    console.error('   macOS: brew install imagemagick');
    console.error('   Ubuntu: sudo apt-get install imagemagick');
    console.error('   Windows: Download from https://imagemagick.org/');
    return false;
  }
}

function main() {
  console.log('🚀 Starting icon generation...\n');

  // Check if ImageMagick is available
  if (!checkImageMagick()) {
    process.exit(1);
  }

  const assetsDir = path.join(__dirname, '..', 'assets', 'images');
  const sourceIconPath = path.join(assetsDir, 'icon-source.png');
  const sourceSplashPath = path.join(assetsDir, 'splash-source.png');

  // Ensure assets directory exists
  ensureDirectoryExists(assetsDir);
  ensureDirectoryExists(path.join(assetsDir, 'ios'));
  ensureDirectoryExists(path.join(assetsDir, 'android'));

  const useSourceIcon = fs.existsSync(sourceIconPath);
  const useSourceSplash = fs.existsSync(sourceSplashPath);

  if (!useSourceIcon) {
    console.log('⚠️ No source icon found at assets/images/icon-source.png');
    console.log('📱 Creating placeholder icons...\n');
  }

  if (!useSourceSplash) {
    console.log('⚠️ No source splash found at assets/images/splash-source.png');
    console.log('🖼️ Creating placeholder splash screens...\n');
  }

  // Generate iOS icons
  console.log('📱 Generating iOS icons...');
  iconSizes.ios.forEach(({ size, name }) => {
    const outputPath = name.includes('icon-ios') 
      ? path.join(assetsDir, name)
      : path.join(assetsDir, 'ios', name);
    
    if (useSourceIcon) {
      generateIcon(sourceIconPath, outputPath, size);
    } else {
      createPlaceholderIcon(outputPath, size, 'AI');
    }
  });

  // Generate Android icons
  console.log('\n🤖 Generating Android icons...');
  iconSizes.android.forEach(({ size, name }) => {
    const outputPath = name.includes('icon.png') || name.includes('adaptive-icon.png')
      ? path.join(assetsDir, name)
      : path.join(assetsDir, 'android', name);
    
    if (useSourceIcon) {
      generateIcon(sourceIconPath, outputPath, size);
    } else {
      createPlaceholderIcon(outputPath, size, 'AI');
    }
  });

  // Generate web icons
  console.log('\n🌐 Generating web icons...');
  iconSizes.web.forEach(({ size, name }) => {
    const outputPath = path.join(assetsDir, name);
    
    if (useSourceIcon) {
      generateIcon(sourceIconPath, outputPath, size);
    } else {
      createPlaceholderIcon(outputPath, size, 'AI');
    }
  });

  // Generate splash screens
  console.log('\n🖼️ Generating splash screens...');
  splashSizes.forEach(({ width, height, name }) => {
    const outputPath = path.join(assetsDir, name);
    
    if (useSourceSplash) {
      generateSplashScreen(sourceSplashPath, outputPath, width, height);
    } else {
      createPlaceholderSplash(outputPath, width, height);
    }
  });

  console.log('\n✅ Icon generation completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Replace placeholder icons with your actual designs');
  console.log('2. Update icon-source.png (1024x1024) and splash-source.png (1242x2436)');
  console.log('3. Run this script again to regenerate all sizes');
  console.log('4. Test icons on different devices and backgrounds');
}

if (require.main === module) {
  main();
}

module.exports = {
  generateIcon,
  generateSplashScreen,
  createPlaceholderIcon,
  createPlaceholderSplash
};
