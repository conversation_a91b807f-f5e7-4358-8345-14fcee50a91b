/**
 * Key Manager
 * 
 * Advanced key management system for encryption keys and API keys
 */

import * as Keychain from 'react-native-keychain';
import ReactNativeBiometrics from 'react-native-biometrics';
import { 
  StorageError,
  SecureStorageOptions 
} from './types';

interface KeyMetadata {
  id: string;
  name: string;
  type: 'encryption' | 'api' | 'signing' | 'auth';
  created: number;
  lastUsed: number;
  expiresAt?: number;
  rotationInterval?: number; // in milliseconds
  requiresBiometric: boolean;
  accessCount: number;
}

interface KeyEntry {
  key: string;
  metadata: KeyMetadata;
}

export class KeyManager {
  private static instance: KeyManager;
  private biometrics: ReactNativeBiometrics;
  private initialized = false;

  private constructor() {
    this.biometrics = new ReactNativeBiometrics();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): KeyManager {
    if (!KeyManager.instance) {
      KeyManager.instance = new KeyManager();
    }
    return KeyManager.instance;
  }

  /**
   * Initialize key manager
   */
  async initialize(): Promise<void> {
    try {
      // Check biometric availability
      const { available, biometryType } = await this.biometrics.isSensorAvailable();
      
      if (available) {
        console.log(`🔐 Biometric authentication available: ${biometryType}`);
      } else {
        console.log('⚠️ Biometric authentication not available');
      }

      this.initialized = true;
      console.log('🔑 Key manager initialized');
    } catch (error) {
      throw new StorageError(
        'Failed to initialize key manager',
        'KEY_MANAGER_INIT_ERROR',
        error as Error
      );
    }
  }

  /**
   * Generate a new encryption key
   */
  async generateEncryptionKey(
    keyId: string,
    options: {
      requiresBiometric?: boolean;
      expiresIn?: number; // milliseconds
      rotationInterval?: number; // milliseconds
    } = {}
  ): Promise<string> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Generate secure random key
      const keyBytes = new Uint8Array(32); // 256-bit key
      crypto.getRandomValues(keyBytes);
      const key = Array.from(keyBytes, byte => byte.toString(16).padStart(2, '0')).join('');

      const metadata: KeyMetadata = {
        id: keyId,
        name: `Encryption Key - ${keyId}`,
        type: 'encryption',
        created: Date.now(),
        lastUsed: Date.now(),
        expiresAt: options.expiresIn ? Date.now() + options.expiresIn : undefined,
        rotationInterval: options.rotationInterval,
        requiresBiometric: options.requiresBiometric ?? true,
        accessCount: 0,
      };

      await this.storeKey(keyId, key, metadata);

      console.log(`🔑 Generated encryption key: ${keyId}`);
      return key;
    } catch (error) {
      throw new StorageError(
        `Failed to generate encryption key: ${keyId}`,
        'KEY_GENERATION_ERROR',
        error as Error
      );
    }
  }

  /**
   * Store API key securely
   */
  async storeAPIKey(
    provider: string,
    apiKey: string,
    options: {
      requiresBiometric?: boolean;
      expiresIn?: number;
    } = {}
  ): Promise<void> {
    try {
      const keyId = `api_${provider}`;
      
      const metadata: KeyMetadata = {
        id: keyId,
        name: `API Key - ${provider}`,
        type: 'api',
        created: Date.now(),
        lastUsed: Date.now(),
        expiresAt: options.expiresIn ? Date.now() + options.expiresIn : undefined,
        requiresBiometric: options.requiresBiometric ?? false,
        accessCount: 0,
      };

      await this.storeKey(keyId, apiKey, metadata);

      console.log(`🔑 Stored API key for: ${provider}`);
    } catch (error) {
      throw new StorageError(
        `Failed to store API key for: ${provider}`,
        'API_KEY_STORE_ERROR',
        error as Error
      );
    }
  }

  /**
   * Retrieve key with authentication
   */
  async getKey(keyId: string): Promise<string | null> {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Get metadata first
      const metadata = await this.getKeyMetadata(keyId);
      if (!metadata) {
        return null;
      }

      // Check if key is expired
      if (metadata.expiresAt && Date.now() > metadata.expiresAt) {
        console.log(`🔑 Key expired: ${keyId}`);
        await this.deleteKey(keyId);
        return null;
      }

      // Check if biometric authentication is required
      if (metadata.requiresBiometric) {
        const authenticated = await this.authenticateWithBiometrics(
          `Access ${metadata.name}`,
          'Authenticate to access your secure key'
        );
        
        if (!authenticated) {
          throw new StorageError(
            'Biometric authentication failed',
            'BIOMETRIC_AUTH_FAILED'
          );
        }
      }

      // Retrieve the key
      const credentials = await Keychain.getInternetCredentials(keyId);
      if (!credentials) {
        return null;
      }

      // Update access metadata
      await this.updateKeyAccess(keyId);

      return credentials.password;
    } catch (error) {
      throw new StorageError(
        `Failed to retrieve key: ${keyId}`,
        'KEY_RETRIEVAL_ERROR',
        error as Error
      );
    }
  }

  /**
   * Store key with metadata
   */
  private async storeKey(keyId: string, key: string, metadata: KeyMetadata): Promise<void> {
    try {
      // Store the key in Keychain
      const keychainOptions: Keychain.Options = {
        service: 'lifeai-key-manager',
        accessControl: metadata.requiresBiometric 
          ? Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE
          : Keychain.ACCESS_CONTROL.DEVICE_PASSCODE,
        authenticatePrompt: `Access ${metadata.name}`,
      };

      await Keychain.setInternetCredentials(keyId, 'key', key, keychainOptions);

      // Store metadata separately (unencrypted for quick access)
      await Keychain.setInternetCredentials(
        `${keyId}_metadata`,
        'metadata',
        JSON.stringify(metadata),
        { service: 'lifeai-key-metadata' }
      );
    } catch (error) {
      throw new StorageError(
        `Failed to store key: ${keyId}`,
        'KEY_STORE_ERROR',
        error as Error
      );
    }
  }

  /**
   * Get key metadata
   */
  private async getKeyMetadata(keyId: string): Promise<KeyMetadata | null> {
    try {
      const credentials = await Keychain.getInternetCredentials(`${keyId}_metadata`);
      if (!credentials) {
        return null;
      }

      return JSON.parse(credentials.password) as KeyMetadata;
    } catch (error) {
      console.error(`Failed to get metadata for key: ${keyId}`, error);
      return null;
    }
  }

  /**
   * Update key access metadata
   */
  private async updateKeyAccess(keyId: string): Promise<void> {
    try {
      const metadata = await this.getKeyMetadata(keyId);
      if (!metadata) return;

      metadata.lastUsed = Date.now();
      metadata.accessCount++;

      await Keychain.setInternetCredentials(
        `${keyId}_metadata`,
        'metadata',
        JSON.stringify(metadata),
        { service: 'lifeai-key-metadata' }
      );
    } catch (error) {
      console.error(`Failed to update access metadata for key: ${keyId}`, error);
    }
  }

  /**
   * Authenticate with biometrics
   */
  private async authenticateWithBiometrics(
    title: string,
    subtitle: string
  ): Promise<boolean> {
    try {
      const { success } = await this.biometrics.simplePrompt({
        promptMessage: title,
        fallbackPromptMessage: subtitle,
      });

      return success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  }

  /**
   * Delete key and metadata
   */
  async deleteKey(keyId: string): Promise<void> {
    try {
      await Keychain.resetInternetCredentials(keyId);
      await Keychain.resetInternetCredentials(`${keyId}_metadata`);
      
      console.log(`🗑️ Deleted key: ${keyId}`);
    } catch (error) {
      throw new StorageError(
        `Failed to delete key: ${keyId}`,
        'KEY_DELETE_ERROR',
        error as Error
      );
    }
  }

  /**
   * List all stored keys
   */
  async listKeys(): Promise<KeyMetadata[]> {
    try {
      const allServices = await Keychain.getAllInternetCredentials();
      const metadataKeys = Object.keys(allServices).filter(key => key.endsWith('_metadata'));
      
      const keys: KeyMetadata[] = [];
      
      for (const metadataKey of metadataKeys) {
        try {
          const credentials = allServices[metadataKey];
          const metadata = JSON.parse(credentials.password) as KeyMetadata;
          keys.push(metadata);
        } catch (error) {
          console.error(`Failed to parse metadata for: ${metadataKey}`, error);
        }
      }

      return keys;
    } catch (error) {
      throw new StorageError(
        'Failed to list keys',
        'KEY_LIST_ERROR',
        error as Error
      );
    }
  }

  /**
   * Rotate encryption key
   */
  async rotateKey(keyId: string): Promise<string> {
    try {
      const metadata = await this.getKeyMetadata(keyId);
      if (!metadata) {
        throw new StorageError(`Key not found: ${keyId}`, 'KEY_NOT_FOUND');
      }

      // Generate new key
      const newKey = await this.generateEncryptionKey(`${keyId}_new`, {
        requiresBiometric: metadata.requiresBiometric,
        expiresIn: metadata.expiresAt ? metadata.expiresAt - Date.now() : undefined,
        rotationInterval: metadata.rotationInterval,
      });

      // Keep old key for a grace period (for decrypting old data)
      const oldKeyId = `${keyId}_old_${Date.now()}`;
      const oldKey = await this.getKey(keyId);
      
      if (oldKey) {
        await this.storeKey(oldKeyId, oldKey, {
          ...metadata,
          id: oldKeyId,
          name: `${metadata.name} (Old)`,
          expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days grace period
        });
      }

      // Replace current key with new key
      await this.deleteKey(keyId);
      await this.storeKey(keyId, newKey, metadata);

      console.log(`🔄 Rotated key: ${keyId}`);
      return newKey;
    } catch (error) {
      throw new StorageError(
        `Failed to rotate key: ${keyId}`,
        'KEY_ROTATION_ERROR',
        error as Error
      );
    }
  }

  /**
   * Check if keys need rotation
   */
  async checkKeyRotation(): Promise<string[]> {
    try {
      const keys = await this.listKeys();
      const keysNeedingRotation: string[] = [];

      for (const metadata of keys) {
        if (metadata.rotationInterval && metadata.type === 'encryption') {
          const timeSinceCreation = Date.now() - metadata.created;
          if (timeSinceCreation >= metadata.rotationInterval) {
            keysNeedingRotation.push(metadata.id);
          }
        }
      }

      return keysNeedingRotation;
    } catch (error) {
      console.error('Failed to check key rotation:', error);
      return [];
    }
  }

  /**
   * Cleanup expired keys
   */
  async cleanupExpiredKeys(): Promise<number> {
    try {
      const keys = await this.listKeys();
      let cleanedCount = 0;

      for (const metadata of keys) {
        if (metadata.expiresAt && Date.now() > metadata.expiresAt) {
          await this.deleteKey(metadata.id);
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} expired keys`);
      }

      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup expired keys:', error);
      return 0;
    }
  }

  /**
   * Get key manager statistics
   */
  async getStatistics(): Promise<{
    totalKeys: number;
    keysByType: Record<string, number>;
    expiredKeys: number;
    keysNeedingRotation: number;
    biometricKeys: number;
  }> {
    try {
      const keys = await this.listKeys();
      const now = Date.now();

      const stats = {
        totalKeys: keys.length,
        keysByType: {} as Record<string, number>,
        expiredKeys: 0,
        keysNeedingRotation: 0,
        biometricKeys: 0,
      };

      for (const metadata of keys) {
        // Count by type
        stats.keysByType[metadata.type] = (stats.keysByType[metadata.type] || 0) + 1;

        // Count expired keys
        if (metadata.expiresAt && now > metadata.expiresAt) {
          stats.expiredKeys++;
        }

        // Count keys needing rotation
        if (metadata.rotationInterval && metadata.type === 'encryption') {
          const timeSinceCreation = now - metadata.created;
          if (timeSinceCreation >= metadata.rotationInterval) {
            stats.keysNeedingRotation++;
          }
        }

        // Count biometric keys
        if (metadata.requiresBiometric) {
          stats.biometricKeys++;
        }
      }

      return stats;
    } catch (error) {
      console.error('Failed to get key manager statistics:', error);
      return {
        totalKeys: 0,
        keysByType: {},
        expiredKeys: 0,
        keysNeedingRotation: 0,
        biometricKeys: 0,
      };
    }
  }

  /**
   * Reset all keys (for testing or emergency)
   */
  async resetAllKeys(): Promise<void> {
    try {
      const keys = await this.listKeys();
      
      for (const metadata of keys) {
        await this.deleteKey(metadata.id);
      }

      console.log(`🔄 Reset ${keys.length} keys`);
    } catch (error) {
      throw new StorageError(
        'Failed to reset all keys',
        'KEY_RESET_ERROR',
        error as Error
      );
    }
  }
}

// Export singleton instance
export const keyManager = KeyManager.getInstance();
