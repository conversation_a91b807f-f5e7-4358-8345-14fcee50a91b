/**
 * Application Configuration
 * 
 * Central configuration file for the LifeAI Assistant application.
 * Contains all environment-specific settings, API endpoints, and feature flags.
 */

import { Platform } from 'react-native';

/**
 * Environment configuration
 */
export const ENV = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
} as const;

/**
 * Current environment (should be set via build process)
 */
export const CURRENT_ENV = __DEV__ ? ENV.DEVELOPMENT : ENV.PRODUCTION;

/**
 * API Configuration
 */
export const API_CONFIG = {
  BASE_URL: {
    [ENV.DEVELOPMENT]: 'https://dev-api.lifeai.app',
    [ENV.STAGING]: 'https://staging-api.lifeai.app',
    [ENV.PRODUCTION]: 'https://api.lifeai.app',
  },
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

/**
 * AI Service Configuration
 */
export const AI_CONFIG = {
  MODELS: {
    CHAT: 'gpt-4-turbo',
    VOICE: 'whisper-1',
    IMAGE: 'gpt-4-vision',
    EMBEDDING: 'text-embedding-ada-002',
  },
  MAX_TOKENS: {
    CHAT: 4000,
    SUMMARY: 500,
    QUICK_RESPONSE: 150,
  },
  TEMPERATURE: 0.7,
  STREAMING: true,
} as const;

/**
 * Storage Configuration
 */
export const STORAGE_CONFIG = {
  KEYS: {
    USER_TOKEN: 'user_token',
    REFRESH_TOKEN: 'refresh_token',
    USER_PREFERENCES: 'user_preferences',
    CHAT_HISTORY: 'chat_history',
    BIOMETRIC_ENABLED: 'biometric_enabled',
    LANGUAGE: 'app_language',
    THEME: 'app_theme',
  },
  ENCRYPTION: {
    ENABLED: true,
    ALGORITHM: 'AES-256-GCM',
  },
  CACHE: {
    TTL: 24 * 60 * 60 * 1000, // 24 hours
    MAX_SIZE: 50 * 1024 * 1024, // 50MB
  },
} as const;

/**
 * Feature Flags
 */
export const FEATURE_FLAGS = {
  BIOMETRIC_AUTH: true,
  VOICE_MESSAGES: true,
  IMAGE_ANALYSIS: true,
  OFFLINE_MODE: true,
  ANALYTICS: true,
  CRASH_REPORTING: true,
  PERFORMANCE_MONITORING: true,
  PUSH_NOTIFICATIONS: true,
  DARK_MODE: true,
  RTL_SUPPORT: true,
  SECURE_STORAGE: true,
  SAFE_MODE: true,
  LOCAL_AI: true,
  PRIVACY_CONTROLS: true,
  ENCRYPTED_DATABASE: true,
} as const;

/**
 * UI Configuration
 */
export const UI_CONFIG = {
  ANIMATION: {
    DURATION: {
      SHORT: 200,
      MEDIUM: 300,
      LONG: 500,
    },
    EASING: 'ease-in-out',
  },
  HAPTIC: {
    ENABLED: true,
    TYPES: {
      LIGHT: 'light',
      MEDIUM: 'medium',
      HEAVY: 'heavy',
      SUCCESS: 'success',
      WARNING: 'warning',
      ERROR: 'error',
    },
  },
  SAFE_AREA: {
    TOP: Platform.OS === 'ios' ? 44 : 24,
    BOTTOM: Platform.OS === 'ios' ? 34 : 0,
  },
} as const;

/**
 * Security Configuration
 */
export const SECURITY_CONFIG = {
  SESSION: {
    TIMEOUT: 30 * 60 * 1000, // 30 minutes
    REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes before expiry
  },
  BIOMETRIC: {
    FALLBACK_TITLE: 'Use Passcode',
    PROMPT_MESSAGE: 'Authenticate to access LifeAI Assistant',
  },
  ENCRYPTION: {
    KEY_SIZE: 256,
    ITERATIONS: 10000,
  },
} as const;

/**
 * Localization Configuration
 */
export const I18N_CONFIG = {
  DEFAULT_LANGUAGE: 'en',
  SUPPORTED_LANGUAGES: ['en', 'ar'],
  FALLBACK_LANGUAGE: 'en',
  NAMESPACE_SEPARATOR: ':',
  KEY_SEPARATOR: '.',
  INTERPOLATION: {
    PREFIX: '{{',
    SUFFIX: '}}',
  },
} as const;

/**
 * Analytics Configuration
 */
export const ANALYTICS_CONFIG = {
  ENABLED: FEATURE_FLAGS.ANALYTICS && !__DEV__,
  PROVIDERS: {
    FIREBASE: true,
    MIXPANEL: false,
    AMPLITUDE: false,
  },
  EVENTS: {
    APP_OPEN: 'app_open',
    USER_LOGIN: 'user_login',
    USER_LOGOUT: 'user_logout',
    CHAT_MESSAGE_SENT: 'chat_message_sent',
    AI_RESPONSE_RECEIVED: 'ai_response_received',
    FEATURE_USED: 'feature_used',
    ERROR_OCCURRED: 'error_occurred',
  },
} as const;

/**
 * Performance Configuration
 */
export const PERFORMANCE_CONFIG = {
  MONITORING: {
    ENABLED: FEATURE_FLAGS.PERFORMANCE_MONITORING,
    SAMPLE_RATE: CURRENT_ENV === ENV.PRODUCTION ? 0.1 : 1.0,
  },
  METRICS: {
    APP_START_TIME: 'app_start_time',
    SCREEN_LOAD_TIME: 'screen_load_time',
    API_RESPONSE_TIME: 'api_response_time',
    AI_RESPONSE_TIME: 'ai_response_time',
  },
} as const;

/**
 * Default export with all configurations
 */
const AppConfig = {
  ENV: CURRENT_ENV,
  API: API_CONFIG,
  AI: AI_CONFIG,
  STORAGE: STORAGE_CONFIG,
  FEATURES: FEATURE_FLAGS,
  UI: UI_CONFIG,
  SECURITY: SECURITY_CONFIG,
  I18N: I18N_CONFIG,
  ANALYTICS: ANALYTICS_CONFIG,
  PERFORMANCE: PERFORMANCE_CONFIG,
} as const;

export default AppConfig;
