#!/usr/bin/env node

/**
 * Create Simple Assets Script
 * 
 * Creates basic placeholder assets for the app
 */

const fs = require('fs');
const path = require('path');

// Create assets directory if it doesn't exist
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

// Create a simple SVG icon
const iconSVG = `
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <rect width="1024" height="1024" fill="#2196F3"/>
  <circle cx="512" cy="400" r="150" fill="white"/>
  <rect x="412" y="550" width="200" height="100" rx="50" fill="white"/>
  <text x="512" y="750" text-anchor="middle" fill="white" font-size="80" font-family="Arial, sans-serif">AI</text>
</svg>
`;

// Create a simple splash screen SVG
const splashSVG = `
<svg width="1242" height="2436" viewBox="0 0 1242 2436" xmlns="http://www.w3.org/2000/svg">
  <rect width="1242" height="2436" fill="white"/>
  <circle cx="621" cy="1000" r="200" fill="#2196F3"/>
  <text x="621" y="1350" text-anchor="middle" fill="#2196F3" font-size="100" font-family="Arial, sans-serif">LifeAI</text>
  <text x="621" y="1450" text-anchor="middle" fill="#666" font-size="60" font-family="Arial, sans-serif">Assistant</text>
</svg>
`;

// Create favicon SVG
const faviconSVG = `
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <rect width="32" height="32" fill="#2196F3"/>
  <circle cx="16" cy="12" r="5" fill="white"/>
  <rect x="13" y="18" width="6" height="3" rx="1.5" fill="white"/>
  <text x="16" y="28" text-anchor="middle" fill="white" font-size="8" font-family="Arial, sans-serif">AI</text>
</svg>
`;

// Write SVG files
fs.writeFileSync(path.join(assetsDir, 'icon.svg'), iconSVG);
fs.writeFileSync(path.join(assetsDir, 'splash.svg'), splashSVG);
fs.writeFileSync(path.join(assetsDir, 'favicon.svg'), faviconSVG);
fs.writeFileSync(path.join(assetsDir, 'adaptive-icon.svg'), iconSVG);

console.log('✅ Simple assets created successfully!');
console.log('📁 Assets created in:', assetsDir);
console.log('');
console.log('📝 Note: These are SVG placeholders.');
console.log('   For production, convert them to PNG:');
console.log('   - icon.png (1024x1024)');
console.log('   - splash.png (1242x2436)');
console.log('   - favicon.png (32x32)');
console.log('   - adaptive-icon.png (1024x1024)');
console.log('');
console.log('🔧 You can use online tools like:');
console.log('   - https://convertio.co/svg-png/');
console.log('   - https://cloudconvert.com/svg-to-png');
console.log('   - Or any image editor that supports SVG');

// Create a simple README for assets
const assetsReadme = `# Assets

This folder contains the app assets:

## Icons
- \`icon.svg\` - Main app icon (convert to 1024x1024 PNG)
- \`adaptive-icon.svg\` - Android adaptive icon (convert to 1024x1024 PNG)
- \`favicon.svg\` - Web favicon (convert to 32x32 PNG)

## Splash Screen
- \`splash.svg\` - Splash screen (convert to 1242x2436 PNG)

## Converting SVG to PNG

You can use online tools or command line:

### Online Tools
- https://convertio.co/svg-png/
- https://cloudconvert.com/svg-to-png

### Command Line (if you have ImageMagick)
\`\`\`bash
# Convert icon
convert icon.svg -resize 1024x1024 icon.png

# Convert splash
convert splash.svg -resize 1242x2436 splash.png

# Convert favicon
convert favicon.svg -resize 32x32 favicon.png

# Convert adaptive icon
convert adaptive-icon.svg -resize 1024x1024 adaptive-icon.png
\`\`\`

## For Production
Replace these placeholder assets with your actual app design.
`;

fs.writeFileSync(path.join(assetsDir, 'README.md'), assetsReadme);

console.log('📖 Assets README created');
console.log('');
console.log('🚀 Ready to build APK!');
