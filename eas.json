{"cli": {"version": ">= 5.9.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium"}, "android": {"buildType": "developmentBuild", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true}, "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"ios": {"resourceClass": "m-medium", "autoIncrement": "buildNumber"}, "android": {"buildType": "app-bundle", "autoIncrement": "versionCode"}, "env": {"NODE_ENV": "production"}}, "production-ios": {"extends": "production", "ios": {"resourceClass": "m-medium", "autoIncrement": "buildNumber", "bundleIdentifier": "com.lifeai.assistant"}}, "production-android": {"extends": "production", "android": {"buildType": "app-bundle", "autoIncrement": "versionCode", "applicationId": "com.lifeai.assistant"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./android/service-account-key.json", "track": "production"}}, "preview": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./android/service-account-key.json", "track": "internal"}}}}