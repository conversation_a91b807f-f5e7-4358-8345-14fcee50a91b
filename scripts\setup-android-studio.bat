@echo off
echo 🚀 Setting up Android Studio environment for LifeAI Assistant...
echo.

REM Set Android SDK path
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set ANDROID_SDK_ROOT=%ANDROID_HOME%

REM Add to PATH
set PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%ANDROID_HOME%\tools\bin;%PATH%

echo ✅ Android environment variables set:
echo    ANDROID_HOME: %ANDROID_HOME%
echo    ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
echo.

echo 🔍 Checking Android SDK components...

REM Check ADB
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo ✅ ADB found
    "%ANDROID_HOME%\platform-tools\adb.exe" version
) else (
    echo ❌ ADB not found
)
echo.

REM Check available platforms
echo 📱 Available Android platforms:
if exist "%ANDROID_HOME%\platforms" (
    dir /b "%ANDROID_HOME%\platforms"
) else (
    echo ❌ No platforms found
)
echo.

REM Check build tools
echo 🔧 Available build tools:
if exist "%ANDROID_HOME%\build-tools" (
    dir /b "%ANDROID_HOME%\build-tools"
) else (
    echo ❌ No build tools found
)
echo.

REM Check emulators
echo 📱 Available emulators:
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
) else (
    echo ❌ Emulator not found
)
echo.

echo 🎯 Next steps:
echo 1. Open Android Studio
echo 2. Create or start an emulator
echo 3. Run: npm run android
echo.

echo 💡 Useful commands:
echo    npm run android          - Run on emulator/device
echo    adb devices              - List connected devices
echo    emulator -list-avds      - List available emulators
echo.

pause
