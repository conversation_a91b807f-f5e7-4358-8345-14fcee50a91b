#!/usr/bin/env node

/**
 * Simple Android Test Script
 * 
 * Creates a minimal React Native app for testing
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Creating simple Android test...\n');

// Step 1: Check emulator status
console.log('📱 Checking emulator status...');
try {
  const devices = execSync('adb devices', { encoding: 'utf8' });
  console.log(devices);
  
  if (devices.includes('emulator-') || devices.includes('device')) {
    console.log('✅ Emulator/device found');
  } else {
    console.log('❌ No emulator/device found');
    console.log('🔧 Starting emulator...');
    
    // Try to start emulator
    const { spawn } = require('child_process');
    const emulator = spawn('C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\emulator\\emulator.exe', ['-avd', 'Pixel_6a_API_35'], {
      detached: true,
      stdio: 'ignore'
    });
    emulator.unref();
    
    console.log('⏳ Waiting for emulator to start...');
    console.log('   This may take 1-2 minutes');
    
    // Wait for emulator
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        const newDevices = execSync('adb devices', { encoding: 'utf8' });
        if (newDevices.includes('emulator-') || newDevices.includes('device')) {
          console.log('✅ Emulator started successfully!');
          break;
        }
      } catch (error) {
        // Continue waiting
      }
      
      process.stdout.write('.');
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }
    
    if (attempts >= maxAttempts) {
      console.log('\n❌ Emulator failed to start');
      console.log('💡 Please start emulator manually:');
      console.log('   1. Open Android Studio');
      console.log('   2. Go to Tools → AVD Manager');
      console.log('   3. Click ▶️ next to any emulator');
      process.exit(1);
    }
  }
} catch (error) {
  console.error('❌ Error checking devices:', error.message);
  process.exit(1);
}

// Step 2: Create simple app structure
console.log('\n🏗️ Setting up simple app structure...');

// Create a very simple App.tsx
const simpleApp = `
import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

const App = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>🤖 LifeAI Assistant</Text>
        <Text style={styles.subtitle}>مساعد الحياة الذكي</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>✅ App is running successfully!</Text>
          <Text style={styles.sectionTitle}>✅ التطبيق يعمل بنجاح!</Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.featureTitle}>Features / الميزات:</Text>
          <Text style={styles.feature}>🧠 Smart Planner / المخطط الذكي</Text>
          <Text style={styles.feature}>❤️ Health AI / الذكاء الاصطناعي الصحي</Text>
          <Text style={styles.feature}>📚 Learning AI / ذكاء التعلم</Text>
          <Text style={styles.feature}>🛒 Smart Shopping / التسوق الذكي</Text>
          <Text style={styles.feature}>💬 Chat AI / الذكاء الاصطناعي للمحادثة</Text>
          <Text style={styles.feature}>🔒 Privacy & Local AI / الخصوصية والذكاء الاصطناعي المحلي</Text>
          <Text style={styles.feature}>📱 Camera/AR / الكاميرا/الواقع المعزز</Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.info}>Platform: Android</Text>
          <Text style={styles.info}>Environment: Development</Text>
          <Text style={styles.info}>Hot Reload: Enabled</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 30,
    textAlign: 'center',
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    marginVertical: 10,
    borderRadius: 10,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    textAlign: 'center',
    marginBottom: 10,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    textAlign: 'left',
  },
  info: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
});

export default App;
`;

fs.writeFileSync('App.tsx', simpleApp);
console.log('✅ Created simple App.tsx');

// Step 3: Try to run the app
console.log('\n🚀 Attempting to run app...');

try {
  console.log('📦 Starting Metro bundler...');
  
  // Start Metro in background
  const { spawn } = require('child_process');
  const metro = spawn('npm', ['start'], {
    detached: true,
    stdio: 'pipe'
  });
  
  // Wait a bit for Metro to start
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  console.log('🏃 Running app on Android...');
  execSync('npx react-native run-android', { stdio: 'inherit' });
  
} catch (error) {
  console.error('❌ Error running app:', error.message);
  
  console.log('\n🔧 Troubleshooting steps:');
  console.log('1. Make sure emulator is running');
  console.log('2. Check: adb devices');
  console.log('3. Try: npm start (in separate terminal)');
  console.log('4. Try: npx react-native run-android (in another terminal)');
}

// Helper function for async/await in older Node versions
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

console.log('\n🎯 Manual steps if automatic failed:');
console.log('1. Open 2 terminals');
console.log('2. Terminal 1: npm start');
console.log('3. Terminal 2: npx react-native run-android');
console.log('4. Wait for app to appear on emulator');
