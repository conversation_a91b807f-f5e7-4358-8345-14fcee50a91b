/**
 * i18n Configuration
 * 
 * Dynamic translation system with Arabic and English support
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';
import { storageManager } from '@/core/storage';
import { STORAGE_KEYS } from '@/core/storage/types';

// Import translation files
import enCommon from './en/common.json';
import enAuth from './en/auth.json';
import enDashboard from './en/dashboard.json';
import enSettings from './en/settings.json';
import enPrivacy from './en/privacy.json';
import enChat from './en/chat.json';
import enProfile from './en/profile.json';
import enFeatures from './en/features.json';
import enErrors from './en/errors.json';

import arCommon from './ar/common.json';
import arAuth from './ar/auth.json';
import arDashboard from './ar/dashboard.json';
import arSettings from './ar/settings.json';
import arPrivacy from './ar/privacy.json';
import arChat from './ar/chat.json';
import arProfile from './ar/profile.json';
import arFeatures from './ar/features.json';
import arErrors from './ar/errors.json';

// Language resources
const resources = {
  en: {
    common: enCommon,
    auth: enAuth,
    dashboard: enDashboard,
    settings: enSettings,
    privacy: enPrivacy,
    chat: enChat,
    profile: enProfile,
    features: enFeatures,
    errors: enErrors,
  },
  ar: {
    common: arCommon,
    auth: arAuth,
    dashboard: arDashboard,
    settings: arSettings,
    privacy: arPrivacy,
    chat: arChat,
    profile: arProfile,
    features: arFeatures,
    errors: arErrors,
  },
};

// Supported languages
export const SUPPORTED_LANGUAGES = [
  {
    code: 'ar',
    name: 'العربية',
    nameEn: 'Arabic',
    flag: '🇸🇦',
    rtl: true,
  },
  {
    code: 'en',
    name: 'English',
    nameEn: 'English',
    flag: '🇺🇸',
    rtl: false,
  },
] as const;

export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number]['code'];

/**
 * Get device default language
 */
const getDeviceLanguage = (): SupportedLanguage => {
  const locales = getLocales();
  
  for (const locale of locales) {
    const languageCode = locale.languageCode.toLowerCase();
    
    // Check if device language is supported
    if (SUPPORTED_LANGUAGES.some(lang => lang.code === languageCode)) {
      return languageCode as SupportedLanguage;
    }
  }
  
  // Default to English if device language is not supported
  return 'en';
};

/**
 * Get saved language from storage
 */
const getSavedLanguage = async (): Promise<SupportedLanguage> => {
  try {
    const savedLanguage = await storageManager.retrieve<string>(STORAGE_KEYS.LANGUAGE);
    
    if (savedLanguage && SUPPORTED_LANGUAGES.some(lang => lang.code === savedLanguage)) {
      return savedLanguage as SupportedLanguage;
    }
  } catch (error) {
    console.warn('Failed to get saved language:', error);
  }
  
  return getDeviceLanguage();
};

/**
 * Save language to storage
 */
export const saveLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    await storageManager.store(STORAGE_KEYS.LANGUAGE, language, false);
    console.log(`💾 Language saved: ${language}`);
  } catch (error) {
    console.error('Failed to save language:', error);
    throw error;
  }
};

/**
 * Change language dynamically
 */
export const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    // Change i18n language
    await i18n.changeLanguage(language);
    
    // Save to storage
    await saveLanguage(language);
    
    // Update RTL layout if needed
    await updateLayoutDirection(language);
    
    console.log(`🌐 Language changed to: ${language}`);
  } catch (error) {
    console.error('Failed to change language:', error);
    throw error;
  }
};

/**
 * Update layout direction for RTL support
 */
const updateLayoutDirection = async (language: SupportedLanguage): Promise<void> => {
  try {
    const languageInfo = SUPPORTED_LANGUAGES.find(lang => lang.code === language);
    const isRTL = languageInfo?.rtl || false;
    
    // For React Native, we'll handle RTL in components
    // This is a placeholder for future RTL implementation
    console.log(`📱 Layout direction: ${isRTL ? 'RTL' : 'LTR'}`);
  } catch (error) {
    console.error('Failed to update layout direction:', error);
  }
};

/**
 * Get current language info
 */
export const getCurrentLanguageInfo = () => {
  const currentLanguage = i18n.language as SupportedLanguage;
  return SUPPORTED_LANGUAGES.find(lang => lang.code === currentLanguage) || SUPPORTED_LANGUAGES[1];
};

/**
 * Check if current language is RTL
 */
export const isRTL = (): boolean => {
  const currentLanguage = i18n.language as SupportedLanguage;
  const languageInfo = SUPPORTED_LANGUAGES.find(lang => lang.code === currentLanguage);
  return languageInfo?.rtl || false;
};

/**
 * Get translation with fallback
 */
export const getTranslation = (key: string, options?: any): string => {
  try {
    return i18n.t(key, options);
  } catch (error) {
    console.warn(`Translation missing for key: ${key}`);
    return key;
  }
};

/**
 * Initialize i18n
 */
export const initializeI18n = async (): Promise<void> => {
  try {
    // Get saved or device language
    const initialLanguage = await getSavedLanguage();
    
    await i18n
      .use(initReactI18next)
      .init({
        resources,
        lng: initialLanguage,
        fallbackLng: 'en',
        
        // Namespace configuration
        defaultNS: 'common',
        ns: ['common', 'auth', 'dashboard', 'settings', 'privacy', 'chat', 'profile', 'features', 'errors'],
        
        // Interpolation options
        interpolation: {
          escapeValue: false, // React already escapes values
        },
        
        // React i18next options
        react: {
          useSuspense: false,
        },
        
        // Debug in development
        debug: __DEV__,
        
        // Key separator
        keySeparator: '.',
        nsSeparator: ':',
        
        // Pluralization
        pluralSeparator: '_',
        
        // Missing key handler
        missingKeyHandler: (lng, ns, key) => {
          if (__DEV__) {
            console.warn(`Missing translation: ${lng}:${ns}:${key}`);
          }
        },
        
        // Save missing keys in development
        saveMissing: __DEV__,
        
        // Update missing keys
        updateMissing: __DEV__,
      });
    
    // Update layout direction
    await updateLayoutDirection(initialLanguage);
    
    console.log(`🌐 i18n initialized with language: ${initialLanguage}`);
  } catch (error) {
    console.error('Failed to initialize i18n:', error);
    throw error;
  }
};

/**
 * Language change event listeners
 */
const languageChangeListeners: Array<(language: SupportedLanguage) => void> = [];

/**
 * Add language change listener
 */
export const addLanguageChangeListener = (listener: (language: SupportedLanguage) => void): void => {
  languageChangeListeners.push(listener);
};

/**
 * Remove language change listener
 */
export const removeLanguageChangeListener = (listener: (language: SupportedLanguage) => void): void => {
  const index = languageChangeListeners.indexOf(listener);
  if (index > -1) {
    languageChangeListeners.splice(index, 1);
  }
};

/**
 * Notify language change listeners
 */
const notifyLanguageChange = (language: SupportedLanguage): void => {
  languageChangeListeners.forEach(listener => {
    try {
      listener(language);
    } catch (error) {
      console.error('Language change listener error:', error);
    }
  });
};

// Listen to i18n language changes
i18n.on('languageChanged', (language: string) => {
  notifyLanguageChange(language as SupportedLanguage);
});

/**
 * Format number based on current locale
 */
export const formatNumber = (number: number): string => {
  const currentLanguage = i18n.language as SupportedLanguage;
  const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
  
  try {
    return new Intl.NumberFormat(locale).format(number);
  } catch (error) {
    return number.toString();
  }
};

/**
 * Format date based on current locale
 */
export const formatDate = (date: Date, options?: Intl.DateTimeFormatOptions): string => {
  const currentLanguage = i18n.language as SupportedLanguage;
  const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  try {
    return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(date);
  } catch (error) {
    return date.toLocaleDateString();
  }
};

/**
 * Format currency based on current locale
 */
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  const currentLanguage = i18n.language as SupportedLanguage;
  const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
    }).format(amount);
  } catch (error) {
    return `${amount} ${currency}`;
  }
};

/**
 * Get text direction for current language
 */
export const getTextDirection = (): 'ltr' | 'rtl' => {
  return isRTL() ? 'rtl' : 'ltr';
};

/**
 * Get text alignment for current language
 */
export const getTextAlign = (): 'left' | 'right' => {
  return isRTL() ? 'right' : 'left';
};

/**
 * Get flex direction for current language
 */
export const getFlexDirection = (): 'row' | 'row-reverse' => {
  return isRTL() ? 'row-reverse' : 'row';
};

export default i18n;
