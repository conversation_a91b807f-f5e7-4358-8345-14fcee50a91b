/**
 * Privacy Settings Hook
 * 
 * React hook for managing privacy settings and safe mode
 */

import { useState, useEffect, useCallback } from 'react';
import { 
  PrivacySettings, 
  SafeModeConfig,
  DataCategory,
  storageManager 
} from '@/core/storage';

interface UsePrivacySettingsReturn {
  // State
  privacySettings: PrivacySettings | null;
  safeModeConfig: SafeModeConfig | null;
  dataCategories: Array<DataCategory & { canShare: boolean }>;
  privacyScore: number;
  loading: boolean;
  saving: boolean;
  error: string | null;

  // Safe Mode Status
  isSafeModeActive: boolean;
  isOnline: boolean;
  localAICapabilities: any;

  // Actions
  updatePrivacySetting: (key: keyof PrivacySettings, value: boolean) => Promise<void>;
  updateSafeModeConfig: (updates: Partial<SafeModeConfig>) => Promise<void>;
  enableSafeMode: () => Promise<void>;
  disableSafeMode: () => Promise<void>;
  canShareData: (dataCategory: string, forAI?: boolean) => boolean;
  processWithLocalAI: (input: string, type?: 'chat' | 'analyze' | 'translate') => Promise<any>;
  exportSettings: () => Promise<string>;
  importSettings: (settingsJson: string) => Promise<void>;
  resetToDefaults: () => Promise<void>;
  refresh: () => Promise<void>;
}

export const usePrivacySettings = (): UsePrivacySettingsReturn => {
  // State
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings | null>(null);
  const [safeModeConfig, setSafeModeConfig] = useState<SafeModeConfig | null>(null);
  const [dataCategories, setDataCategories] = useState<Array<DataCategory & { canShare: boolean }>>([]);
  const [privacyScore, setPrivacyScore] = useState(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localAICapabilities, setLocalAICapabilities] = useState<any>(null);

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const privacyManager = storageManager.getPrivacyManager();
      const safeModeService = storageManager.getSafeModeService();

      const [
        settings,
        safeMode,
        categories,
        score,
        capabilities,
      ] = await Promise.all([
        privacyManager.getPrivacySettings(),
        privacyManager.getSafeModeConfig(),
        privacyManager.getDataCategoriesWithStatus(),
        privacyManager.getPrivacyScore(),
        safeModeService.getCapabilities(),
      ]);

      setPrivacySettings(settings);
      setSafeModeConfig(safeMode);
      setDataCategories(categories);
      setPrivacyScore(score);
      setLocalAICapabilities(capabilities);
    } catch (err) {
      console.error('Failed to load privacy settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load privacy settings');
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePrivacySetting = useCallback(async (
    key: keyof PrivacySettings, 
    value: boolean
  ) => {
    if (!privacySettings) return;

    try {
      setSaving(true);
      setError(null);

      // Optimistic update
      const newSettings = { ...privacySettings, [key]: value };
      setPrivacySettings(newSettings);

      // Update in storage
      await storageManager.updatePrivacySettings({ [key]: value });

      // Update privacy score
      const newScore = storageManager.getPrivacyScore();
      setPrivacyScore(newScore);

      // Refresh data categories if sharing settings changed
      if (['sendToExternalAI', 'shareWithThirdParties', 'dataCollection'].includes(key)) {
        const privacyManager = storageManager.getPrivacyManager();
        const categories = await privacyManager.getDataCategoriesWithStatus();
        setDataCategories(categories);
      }
    } catch (err) {
      console.error('Failed to update privacy setting:', err);
      // Revert optimistic update
      setPrivacySettings(privacySettings);
      setError(err instanceof Error ? err.message : 'Failed to update privacy setting');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [privacySettings]);

  const updateSafeModeConfig = useCallback(async (updates: Partial<SafeModeConfig>) => {
    if (!safeModeConfig) return;

    try {
      setSaving(true);
      setError(null);

      // Optimistic update
      const newConfig = { ...safeModeConfig, ...updates };
      setSafeModeConfig(newConfig);

      // Update in storage
      const privacyManager = storageManager.getPrivacyManager();
      await privacyManager.updateSafeModeConfig(updates);
    } catch (err) {
      console.error('Failed to update safe mode config:', err);
      // Revert optimistic update
      setSafeModeConfig(safeModeConfig);
      setError(err instanceof Error ? err.message : 'Failed to update safe mode config');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [safeModeConfig]);

  const enableSafeMode = useCallback(async () => {
    try {
      setSaving(true);
      setError(null);

      await storageManager.enableSafeMode();
      
      // Refresh data
      await loadData();
    } catch (err) {
      console.error('Failed to enable safe mode:', err);
      setError(err instanceof Error ? err.message : 'Failed to enable safe mode');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [loadData]);

  const disableSafeMode = useCallback(async () => {
    try {
      setSaving(true);
      setError(null);

      await storageManager.disableSafeMode();
      
      // Refresh data
      await loadData();
    } catch (err) {
      console.error('Failed to disable safe mode:', err);
      setError(err instanceof Error ? err.message : 'Failed to disable safe mode');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [loadData]);

  const canShareData = useCallback((dataCategory: string, forAI: boolean = false) => {
    return storageManager.canShareData(dataCategory, forAI);
  }, []);

  const processWithLocalAI = useCallback(async (
    input: string, 
    type: 'chat' | 'analyze' | 'translate' = 'chat'
  ) => {
    try {
      return await storageManager.processWithLocalAI(input, type);
    } catch (err) {
      console.error('Failed to process with local AI:', err);
      throw err;
    }
  }, []);

  const exportSettings = useCallback(async () => {
    try {
      setSaving(true);
      setError(null);

      return await storageManager.exportData();
    } catch (err) {
      console.error('Failed to export settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to export settings');
      throw err;
    } finally {
      setSaving(false);
    }
  }, []);

  const importSettings = useCallback(async (settingsJson: string) => {
    try {
      setSaving(true);
      setError(null);

      await storageManager.importData(settingsJson);
      
      // Refresh data
      await loadData();
    } catch (err) {
      console.error('Failed to import settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to import settings');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [loadData]);

  const resetToDefaults = useCallback(async () => {
    try {
      setSaving(true);
      setError(null);

      // Reset to default privacy settings
      await storageManager.updatePrivacySettings({
        dataCollection: false,
        analytics: false,
        crashReporting: true,
        performanceMonitoring: false,
        sendToExternalAI: false,
        useLocalAIOnly: true,
        shareConversationHistory: false,
        personalizedResponses: true,
        shareWithThirdParties: false,
        allowTelemetry: false,
        marketingCommunications: false,
        biometricAuth: true,
        autoLock: true,
        autoLockTimeout: 5,
        encryptLocalData: true,
        secureBackup: true,
      });

      // Refresh data
      await loadData();
    } catch (err) {
      console.error('Failed to reset to defaults:', err);
      setError(err instanceof Error ? err.message : 'Failed to reset to defaults');
      throw err;
    } finally {
      setSaving(false);
    }
  }, [loadData]);

  const refresh = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // Computed values
  const isSafeModeActive = safeModeConfig?.enabled || false;
  const privacyManager = storageManager.getPrivacyManager();
  const isOnline = privacyManager.isNetworkAvailable();

  return {
    // State
    privacySettings,
    safeModeConfig,
    dataCategories,
    privacyScore,
    loading,
    saving,
    error,

    // Safe Mode Status
    isSafeModeActive,
    isOnline,
    localAICapabilities,

    // Actions
    updatePrivacySetting,
    updateSafeModeConfig,
    enableSafeMode,
    disableSafeMode,
    canShareData,
    processWithLocalAI,
    exportSettings,
    importSettings,
    resetToDefaults,
    refresh,
  };
};

/**
 * Hook for safe mode specific functionality
 */
export const useSafeMode = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const safeModeService = storageManager.getSafeModeService();
      const statistics = await safeModeService.getStatistics();
      setStats(statistics);
    } catch (error) {
      console.error('Failed to load safe mode stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearCache = async () => {
    try {
      const safeModeService = storageManager.getSafeModeService();
      await safeModeService.clearCache();
      await loadStats();
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw error;
    }
  };

  const addKnowledgeEntry = async (entry: any) => {
    try {
      const safeModeService = storageManager.getSafeModeService();
      await safeModeService.addKnowledgeEntry(entry);
      await loadStats();
    } catch (error) {
      console.error('Failed to add knowledge entry:', error);
      throw error;
    }
  };

  return {
    stats,
    loading,
    clearCache,
    addKnowledgeEntry,
    refresh: loadStats,
  };
};

/**
 * Hook for privacy audit functionality
 */
export const usePrivacyAudit = () => {
  const [auditReport, setAuditReport] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const generateReport = async (days: number = 30) => {
    try {
      setLoading(true);
      const privacyManager = storageManager.getPrivacyManager();
      const report = await privacyManager.getPrivacyAuditReport(days);
      setAuditReport(report);
    } catch (error) {
      console.error('Failed to generate privacy audit report:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    auditReport,
    loading,
    generateReport,
  };
};

export default usePrivacySettings;
