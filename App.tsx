/**
 * LifeAI Assistant - Main App Component
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  SafeAreaView,
} from 'react-native';

const App: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <View style={styles.content}>
        <Text style={styles.title}>🤖 LifeAI Assistant</Text>
        <Text style={styles.subtitle}>مساعد الحياة الذكي</Text>
        <Text style={styles.description}>
          AI-powered mobile app for life management
        </Text>
        <Text style={styles.description}>
          تطبيق ذكي مدعوم بالذكاء الاصطناعي لإدارة الحياة
        </Text>
        <View style={styles.features}>
          <Text style={styles.featureTitle}>Features / الميزات:</Text>
          <Text style={styles.feature}>🧠 Smart Planner / المخطط الذكي</Text>
          <Text style={styles.feature}>❤️ Health AI / الذكاء الاصطناعي الصحي</Text>
          <Text style={styles.feature}>📚 Learning AI / ذكاء التعلم</Text>
          <Text style={styles.feature}>🛒 Smart Shopping / التسوق الذكي</Text>
          <Text style={styles.feature}>💬 Chat AI / الذكاء الاصطناعي للمحادثة</Text>
          <Text style={styles.feature}>🔒 Privacy & Local AI / الخصوصية والذكاء الاصطناعي المحلي</Text>
          <Text style={styles.feature}>📱 Camera/AR / الكاميرا/الواقع المعزز</Text>
        </View>
        <Text style={styles.status}>✅ App is running successfully!</Text>
        <Text style={styles.status}>✅ التطبيق يعمل بنجاح!</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  features: {
    marginTop: 30,
    marginBottom: 30,
    alignItems: 'flex-start',
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    textAlign: 'left',
  },
  status: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default App;
