{"name": "<PERSON><PERSON>-assistant", "version": "1.0.0", "description": "LifeAI Assistant - AI-powered mobile app for life management", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "type-check": "tsc --noEmit", "clean": "react-native clean", "prepare": "husky install", "postinstall": "react-native setup-ios-permissions && pod-install ios"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.17.9", "axios": "^1.6.5", "date-fns": "^3.2.0", "i18next": "^23.7.16", "lodash": "^4.17.21", "react": "18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^14.0.0", "react-native": "0.73.2", "react-native-biometrics": "^3.0.1", "react-native-crypto-js": "^1.0.0", "react-native-device-info": "^10.12.0", "react-native-gesture-handler": "^2.14.1", "react-native-haptic-feedback": "^2.2.0", "react-native-image-picker": "^7.1.0", "react-native-keychain": "^8.1.3", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.0.6", "react-native-mmkv": "^2.11.0", "react-native-orientation-locker": "^1.6.0", "react-native-permissions": "^4.1.1", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-splash-screen": "^3.3.0", "react-native-sqlite-2": "^3.6.2", "react-native-svg": "^14.1.0", "react-native-toast-message": "^2.2.0", "react-native-vector-icons": "^10.0.3", "react-redux": "^9.0.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/lodash": "^4.14.202", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.19.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "husky": "^8.0.3", "jest": "^29.2.1", "lint-staged": "^15.2.0", "metro-react-native-babel-preset": "0.76.8", "pod-install": "^0.1.39", "prettier": "^3.2.4", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["react-native", "typescript", "ai", "assistant", "mobile", "cross-platform"], "author": "LifeAI Team", "license": "MIT"}