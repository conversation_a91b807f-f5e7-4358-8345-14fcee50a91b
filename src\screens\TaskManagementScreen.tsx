/**
 * Task Management Screen - صفحة إدارة المهام
 * 
 * شاشة إدارة المهام مع الذكاء الاصطناعي
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Card, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import TaskCard from '../components/tasks/TaskCard';
import TaskFilters from '../components/tasks/TaskFilters';
import TaskStats from '../components/tasks/TaskStats';
import AITaskSuggestions from '../components/tasks/AITaskSuggestions';
import QuickAddTask from '../components/tasks/QuickAddTask';
import TaskCalendarView from '../components/tasks/TaskCalendarView';

// Hooks
import { useTaskManager } from '@/features/smart-planner/hooks/useTaskManager';
import { askPlannerAI } from '@/services/ai';

// Types
interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  dueDate?: Date;
  estimatedDuration: number;
  category: string;
  tags: string[];
}

interface TaskFilter {
  status: string[];
  priority: string[];
  category: string[];
  dateRange: 'today' | 'week' | 'month' | 'all';
}

const TaskManagementScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  const [filters, setFilters] = useState<TaskFilter>({
    status: ['pending', 'in_progress'],
    priority: [],
    category: [],
    dateRange: 'all',
  });
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);

  // Hooks
  const {
    tasks,
    todayTasks,
    completedTasks,
    overdueTasks,
    isLoading,
    refreshTasks,
    createTask,
    updateTask,
    deleteTask,
  } = useTaskManager();

  // Effects
  useEffect(() => {
    loadAISuggestions();
  }, [tasks]);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshTasks();
      await loadAISuggestions();
    } catch (error) {
      console.error('Failed to refresh tasks:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadAISuggestions = async () => {
    if (tasks.length === 0) return;

    setLoadingSuggestions(true);
    try {
      const response = await askPlannerAI.suggestTasks({
        currentTasks: tasks,
        goals: ['productivity', 'efficiency'],
        timeframe: 'this week',
      });
      setAiSuggestions(response.result.suggestedTasks || []);
    } catch (error) {
      console.error('Failed to load AI suggestions:', error);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  const handleTaskPress = (task: Task) => {
    navigation.navigate('TaskDetails', { taskId: task.id });
  };

  const handleTaskComplete = async (taskId: string) => {
    try {
      await updateTask(taskId, { status: 'completed' });
    } catch (error) {
      console.error('Failed to complete task:', error);
    }
  };

  const handleTaskEdit = (task: Task) => {
    navigation.navigate('EditTask', { task });
  };

  const handleCreateTask = () => {
    navigation.navigate('CreateTask');
  };

  const handleQuickAddTask = async (title: string) => {
    try {
      await createTask({
        title,
        priority: 'medium',
        status: 'pending',
        estimatedDuration: 30,
        category: 'general',
        tags: [],
      });
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const handleFilterChange = (newFilters: TaskFilter) => {
    setFilters(newFilters);
  };

  const handleApplyAISuggestion = async (suggestion: any) => {
    try {
      await createTask({
        title: suggestion.title,
        description: suggestion.description,
        priority: suggestion.priority || 'medium',
        status: 'pending',
        estimatedDuration: suggestion.estimatedDuration || 30,
        category: suggestion.category || 'general',
        tags: suggestion.tags || [],
      });
    } catch (error) {
      console.error('Failed to apply AI suggestion:', error);
    }
  };

  // Filter tasks based on current filters
  const filteredTasks = tasks.filter(task => {
    if (filters.status.length > 0 && !filters.status.includes(task.status)) {
      return false;
    }
    if (filters.priority.length > 0 && !filters.priority.includes(task.priority)) {
      return false;
    }
    if (filters.category.length > 0 && !filters.category.includes(task.category)) {
      return false;
    }
    
    // Date range filter
    if (filters.dateRange !== 'all' && task.dueDate) {
      const now = new Date();
      const taskDate = new Date(task.dueDate);
      
      switch (filters.dateRange) {
        case 'today':
          if (taskDate.toDateString() !== now.toDateString()) return false;
          break;
        case 'week':
          const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
          if (taskDate > weekFromNow) return false;
          break;
        case 'month':
          const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
          if (taskDate > monthFromNow) return false;
          break;
      }
    }
    
    return true;
  });

  const renderTaskItem = ({ item }: { item: Task }) => (
    <TaskCard
      task={item}
      onPress={() => handleTaskPress(item)}
      onComplete={() => handleTaskComplete(item.id)}
      onEdit={() => handleTaskEdit(item)}
      style={styles.taskCard}
    />
  );

  return (
    <Container padding="none">
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text variant="subtitle1" weight="bold" i18nKey="tasks.title" />
          <View style={styles.headerActions}>
            <Button
              title=""
              icon={viewMode === 'list' ? 'calendar' : 'list'}
              variant="ghost"
              size="sm"
              onPress={() => setViewMode(viewMode === 'list' ? 'calendar' : 'list')}
            />
            <Button
              title=""
              icon="plus"
              variant="primary"
              size="sm"
              onPress={handleCreateTask}
            />
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary.main}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Quick Add Task */}
        <View style={styles.section}>
          <QuickAddTask onAddTask={handleQuickAddTask} />
        </View>

        {/* Task Stats */}
        <View style={styles.section}>
          <TaskStats
            totalTasks={tasks.length}
            completedTasks={completedTasks.length}
            todayTasks={todayTasks.length}
            overdueTasks={overdueTasks.length}
          />
        </View>

        {/* AI Suggestions */}
        {aiSuggestions.length > 0 && (
          <View style={styles.section}>
            <AITaskSuggestions
              suggestions={aiSuggestions}
              loading={loadingSuggestions}
              onApplySuggestion={handleApplyAISuggestion}
            />
          </View>
        )}

        {/* Filters */}
        <View style={styles.section}>
          <TaskFilters
            filters={filters}
            onFiltersChange={handleFilterChange}
          />
        </View>

        {/* Tasks List or Calendar */}
        <View style={styles.section}>
          {viewMode === 'list' ? (
            <View>
              <Text
                variant="subtitle2"
                weight="semibold"
                style={styles.sectionTitle}
                i18nKey="tasks.yourTasks"
              />
              {filteredTasks.length > 0 ? (
                <FlatList
                  data={filteredTasks}
                  renderItem={renderTaskItem}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <Card style={styles.emptyState}>
                  <Text
                    variant="body2"
                    color="textSecondary"
                    align="center"
                    i18nKey="tasks.noTasks"
                  />
                  <Button
                    title={t('tasks.createFirstTask')}
                    variant="outline"
                    size="sm"
                    onPress={handleCreateTask}
                    style={styles.emptyStateButton}
                  />
                </Card>
              )}
            </View>
          ) : (
            <TaskCalendarView
              tasks={filteredTasks}
              onTaskPress={handleTaskPress}
              onDatePress={(date) => {
                // Handle date press for creating new task
                navigation.navigate('CreateTask', { selectedDate: date });
              }}
            />
          )}
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  taskCard: {
    marginBottom: 12,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateButton: {
    marginTop: 16,
    minWidth: 150,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default TaskManagementScreen;
