# Fastfile for LifeAI Assistant
# Automated deployment to App Store and Google Play

default_platform(:ios)

# Global variables
APP_NAME = "LifeAI Assistant"
BUNDLE_ID = "com.lifeai.assistant"
PACKAGE_NAME = "com.lifeai.assistant"

platform :ios do
  desc "Build and upload to TestFlight"
  lane :beta do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Ensure clean git status
    ensure_git_status_clean
    
    # Increment build number
    increment_build_number(xcodeproj: "ios/LifeAIAssistant.xcodeproj")
    
    # Build the app
    build_app(
      scheme: "LifeAIAssistant",
      workspace: "ios/LifeAIAssistant.xcworkspace",
      export_method: "app-store",
      export_options: {
        provisioningProfiles: {
          BUNDLE_ID => "LifeAI Assistant App Store"
        }
      }
    )
    
    # Upload to TestFlight
    upload_to_testflight(
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      distribute_external: false,
      notify_external_testers: false,
      changelog: "Bug fixes and performance improvements"
    )
    
    # Commit version bump
    commit_version_bump(
      message: "Version bump for TestFlight build",
      xcodeproj: "ios/LifeAIAssistant.xcodeproj"
    )
    
    # Push to git
    push_to_git_remote
    
    # Send notification
    slack(
      message: "✅ #{APP_NAME} iOS beta build uploaded to TestFlight!",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Deploy to App Store"
  lane :release do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Ensure clean git status
    ensure_git_status_clean
    
    # Increment version number
    increment_version_number(xcodeproj: "ios/LifeAIAssistant.xcodeproj")
    
    # Increment build number
    increment_build_number(xcodeproj: "ios/LifeAIAssistant.xcodeproj")
    
    # Build the app
    build_app(
      scheme: "LifeAIAssistant",
      workspace: "ios/LifeAIAssistant.xcworkspace",
      export_method: "app-store"
    )
    
    # Upload to App Store
    upload_to_app_store(
      submit_for_review: false,
      automatic_release: false,
      force: true,
      metadata_path: "./fastlane/metadata",
      screenshot_path: "./fastlane/screenshots"
    )
    
    # Commit version bump
    commit_version_bump(
      message: "Version bump for App Store release",
      xcodeproj: "ios/LifeAIAssistant.xcodeproj"
    )
    
    # Create git tag
    add_git_tag(
      tag: get_version_number(xcodeproj: "ios/LifeAIAssistant.xcodeproj")
    )
    
    # Push to git
    push_to_git_remote(tags: true)
    
    # Send notification
    slack(
      message: "🚀 #{APP_NAME} iOS released to App Store!",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Generate screenshots"
  lane :screenshots do
    capture_screenshots(
      workspace: "ios/LifeAIAssistant.xcworkspace",
      scheme: "LifeAIAssistantUITests"
    )
  end

  desc "Setup certificates and provisioning profiles"
  lane :certificates do
    match(
      type: "development",
      app_identifier: BUNDLE_ID
    )
    
    match(
      type: "appstore",
      app_identifier: BUNDLE_ID
    )
  end
end

platform :android do
  desc "Build and upload to Google Play Internal Testing"
  lane :beta do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Ensure clean git status
    ensure_git_status_clean
    
    # Increment version code
    increment_version_code(
      gradle_file_path: "android/app/build.gradle"
    )
    
    # Build the app
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Google Play
    upload_to_play_store(
      track: "internal",
      aab: "android/app/build/outputs/bundle/release/app-release.aab",
      skip_upload_metadata: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
    
    # Commit version bump
    git_commit(
      path: "android/app/build.gradle",
      message: "Version bump for Google Play beta"
    )
    
    # Push to git
    push_to_git_remote
    
    # Send notification
    slack(
      message: "✅ #{APP_NAME} Android beta uploaded to Google Play!",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Deploy to Google Play Store"
  lane :release do
    # Ensure we're on the right branch
    ensure_git_branch(branch: 'main')
    
    # Ensure clean git status
    ensure_git_status_clean
    
    # Increment version name and code
    increment_version_name(
      gradle_file_path: "android/app/build.gradle"
    )
    
    increment_version_code(
      gradle_file_path: "android/app/build.gradle"
    )
    
    # Build the app
    gradle(
      task: "bundle",
      build_type: "Release",
      project_dir: "android/"
    )
    
    # Upload to Google Play
    upload_to_play_store(
      track: "production",
      aab: "android/app/build/outputs/bundle/release/app-release.aab",
      metadata_path: "./fastlane/metadata/android",
      skip_upload_images: false,
      skip_upload_screenshots: false
    )
    
    # Commit version bump
    git_commit(
      path: "android/app/build.gradle",
      message: "Version bump for Google Play release"
    )
    
    # Create git tag
    version_name = get_version_name(
      gradle_file_path: "android/app/build.gradle"
    )
    add_git_tag(tag: "android-#{version_name}")
    
    # Push to git
    push_to_git_remote(tags: true)
    
    # Send notification
    slack(
      message: "🚀 #{APP_NAME} Android released to Google Play!",
      success: true
    ) if ENV["SLACK_URL"]
  end

  desc "Generate screenshots for Google Play"
  lane :screenshots do
    gradle(
      task: "connectedAndroidTest",
      project_dir: "android/"
    )
  end
end

# Cross-platform lanes
desc "Run tests on both platforms"
lane :test do
  # Run iOS tests
  run_tests(
    workspace: "ios/LifeAIAssistant.xcworkspace",
    scheme: "LifeAIAssistantTests"
  )
  
  # Run Android tests
  gradle(
    task: "test",
    project_dir: "android/"
  )
end

desc "Deploy to both platforms"
lane :deploy_all do
  # Deploy iOS
  ios do
    release
  end
  
  # Deploy Android
  android do
    release
  end
  
  # Send notification
  slack(
    message: "🎉 #{APP_NAME} deployed to both App Store and Google Play!",
    success: true
  ) if ENV["SLACK_URL"]
end

desc "Setup development environment"
lane :setup do
  # Install dependencies
  sh("npm install")
  
  # Setup iOS certificates
  ios do
    certificates
  end
  
  # Setup Android keystore (if needed)
  android do
    # Add any Android-specific setup here
  end
  
  puts "✅ Development environment setup complete!"
end

# Error handling
error do |lane, exception|
  slack(
    message: "❌ #{APP_NAME} deployment failed in lane '#{lane}': #{exception.message}",
    success: false
  ) if ENV["SLACK_URL"]
end
