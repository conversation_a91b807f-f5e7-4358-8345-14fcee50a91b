/**
 * Advanced Privacy Settings Screen
 * 
 * Comprehensive privacy controls and data sharing management
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  Alert,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { 
  PrivacySettings, 
  DataCategory,
  SafeModeConfig,
  storageManager,
  DATA_CATEGORIES 
} from '@/core/storage';

interface PrivacySettingItem {
  id: keyof PrivacySettings;
  title: string;
  description: string;
  value: boolean;
  category: 'data' | 'ai' | 'sharing' | 'security';
  critical?: boolean;
  requiresRestart?: boolean;
}

interface DataCategoryItem extends DataCategory {
  canShare: boolean;
  userControlled: boolean;
}

const AdvancedPrivacySettingsScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings | null>(null);
  const [safeModeConfig, setSafeModeConfig] = useState<SafeModeConfig | null>(null);
  const [dataCategories, setDataCategories] = useState<DataCategoryItem[]>([]);
  const [privacyScore, setPrivacyScore] = useState(0);

  // Load settings
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      const privacyManager = storageManager.getPrivacyManager();
      
      const [settings, safeMode, categories] = await Promise.all([
        privacyManager.getPrivacySettings(),
        privacyManager.getSafeModeConfig(),
        privacyManager.getDataCategoriesWithStatus(),
      ]);

      setPrivacySettings(settings);
      setSafeModeConfig(safeMode);
      setDataCategories(categories.map(cat => ({
        ...cat,
        userControlled: !cat.required && cat.canBeShared,
      })));
      setPrivacyScore(privacyManager.getPrivacyScore());
    } catch (error) {
      console.error('Failed to load privacy settings:', error);
      Alert.alert(
        t('error.title'),
        t('privacy.errors.loadFailed')
      );
    } finally {
      setLoading(false);
    }
  };

  const updatePrivacySetting = async (key: keyof PrivacySettings, value: boolean) => {
    if (!privacySettings) return;

    try {
      setSaving(true);
      
      const newSettings = { ...privacySettings, [key]: value };
      setPrivacySettings(newSettings);

      await storageManager.updatePrivacySettings({ [key]: value });
      
      // Update privacy score
      setPrivacyScore(storageManager.getPrivacyScore());

      // Show warning for critical settings
      if (key === 'encryptLocalData' && !value) {
        Alert.alert(
          t('privacy.warnings.title'),
          t('privacy.warnings.disableEncryption'),
          [
            { text: t('common.cancel'), style: 'cancel' },
            { text: t('common.confirm'), style: 'destructive' },
          ]
        );
      }
    } catch (error) {
      console.error('Failed to update privacy setting:', error);
      // Revert the change
      setPrivacySettings(privacySettings);
      Alert.alert(
        t('error.title'),
        t('privacy.errors.updateFailed')
      );
    } finally {
      setSaving(false);
    }
  };

  const toggleSafeMode = async () => {
    if (!safeModeConfig) return;

    try {
      setSaving(true);
      
      if (safeModeConfig.enabled) {
        await storageManager.disableSafeMode();
      } else {
        await storageManager.enableSafeMode();
      }

      // Reload settings
      await loadSettings();
    } catch (error) {
      console.error('Failed to toggle safe mode:', error);
      Alert.alert(
        t('error.title'),
        t('privacy.errors.safeModeToggleFailed')
      );
    } finally {
      setSaving(false);
    }
  };

  const exportPrivacySettings = async () => {
    try {
      const exportData = await storageManager.exportData();
      
      // In a real app, you'd use a file picker or share dialog
      Alert.alert(
        t('privacy.export.title'),
        t('privacy.export.success'),
        [
          { text: t('common.ok') },
        ]
      );
    } catch (error) {
      console.error('Failed to export privacy settings:', error);
      Alert.alert(
        t('error.title'),
        t('privacy.errors.exportFailed')
      );
    }
  };

  const resetToDefaults = () => {
    Alert.alert(
      t('privacy.reset.title'),
      t('privacy.reset.confirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('privacy.reset.confirm'),
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              // Reset to default settings
              await storageManager.updatePrivacySettings({
                dataCollection: false,
                analytics: false,
                crashReporting: true,
                performanceMonitoring: false,
                sendToExternalAI: false,
                useLocalAIOnly: true,
                shareConversationHistory: false,
                personalizedResponses: true,
                shareWithThirdParties: false,
                allowTelemetry: false,
                marketingCommunications: false,
                biometricAuth: true,
                autoLock: true,
                autoLockTimeout: 5,
                encryptLocalData: true,
                secureBackup: true,
              });
              
              await loadSettings();
            } catch (error) {
              console.error('Failed to reset privacy settings:', error);
              Alert.alert(
                t('error.title'),
                t('privacy.errors.resetFailed')
              );
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };

  const getPrivacySettingItems = (): PrivacySettingItem[] => {
    if (!privacySettings) return [];

    return [
      // Data Collection
      {
        id: 'dataCollection',
        title: t('privacy.settings.dataCollection'),
        description: t('privacy.settings.dataCollectionDesc'),
        value: privacySettings.dataCollection,
        category: 'data',
      },
      {
        id: 'analytics',
        title: t('privacy.settings.analytics'),
        description: t('privacy.settings.analyticsDesc'),
        value: privacySettings.analytics,
        category: 'data',
      },
      {
        id: 'crashReporting',
        title: t('privacy.settings.crashReporting'),
        description: t('privacy.settings.crashReportingDesc'),
        value: privacySettings.crashReporting,
        category: 'data',
      },

      // AI Processing
      {
        id: 'sendToExternalAI',
        title: t('privacy.settings.sendToExternalAI'),
        description: t('privacy.settings.sendToExternalAIDesc'),
        value: privacySettings.sendToExternalAI,
        category: 'ai',
        critical: true,
      },
      {
        id: 'useLocalAIOnly',
        title: t('privacy.settings.useLocalAIOnly'),
        description: t('privacy.settings.useLocalAIOnlyDesc'),
        value: privacySettings.useLocalAIOnly,
        category: 'ai',
      },
      {
        id: 'shareConversationHistory',
        title: t('privacy.settings.shareConversationHistory'),
        description: t('privacy.settings.shareConversationHistoryDesc'),
        value: privacySettings.shareConversationHistory,
        category: 'ai',
        critical: true,
      },

      // Data Sharing
      {
        id: 'shareWithThirdParties',
        title: t('privacy.settings.shareWithThirdParties'),
        description: t('privacy.settings.shareWithThirdPartiesDesc'),
        value: privacySettings.shareWithThirdParties,
        category: 'sharing',
        critical: true,
      },
      {
        id: 'allowTelemetry',
        title: t('privacy.settings.allowTelemetry'),
        description: t('privacy.settings.allowTelemetryDesc'),
        value: privacySettings.allowTelemetry,
        category: 'sharing',
      },

      // Security
      {
        id: 'encryptLocalData',
        title: t('privacy.settings.encryptLocalData'),
        description: t('privacy.settings.encryptLocalDataDesc'),
        value: privacySettings.encryptLocalData,
        category: 'security',
        critical: true,
        requiresRestart: true,
      },
      {
        id: 'biometricAuth',
        title: t('privacy.settings.biometricAuth'),
        description: t('privacy.settings.biometricAuthDesc'),
        value: privacySettings.biometricAuth,
        category: 'security',
      },
      {
        id: 'autoLock',
        title: t('privacy.settings.autoLock'),
        description: t('privacy.settings.autoLockDesc'),
        value: privacySettings.autoLock,
        category: 'security',
      },
    ];
  };

  const renderPrivacyScore = () => (
    <View style={styles.scoreCard}>
      <View style={styles.scoreHeader}>
        <Icon name="security" size={24} color="#4CAF50" />
        <Text style={styles.scoreTitle}>{t('privacy.score.title')}</Text>
      </View>
      <View style={styles.scoreContent}>
        <Text style={styles.scoreValue}>{privacyScore}/100</Text>
        <Text style={styles.scoreDescription}>
          {privacyScore >= 80 ? t('privacy.score.excellent') :
           privacyScore >= 60 ? t('privacy.score.good') :
           privacyScore >= 40 ? t('privacy.score.fair') :
           t('privacy.score.poor')}
        </Text>
      </View>
    </View>
  );

  const renderSafeModeCard = () => (
    <View style={styles.safeModeCard}>
      <View style={styles.safeModeHeader}>
        <Icon 
          name={safeModeConfig?.enabled ? "shield" : "security"} 
          size={24} 
          color={safeModeConfig?.enabled ? "#4CAF50" : "#FF9800"} 
        />
        <Text style={styles.safeModeTitle}>{t('privacy.safeMode.title')}</Text>
        <Switch
          value={safeModeConfig?.enabled || false}
          onValueChange={toggleSafeMode}
          disabled={saving}
        />
      </View>
      <Text style={styles.safeModeDescription}>
        {safeModeConfig?.enabled 
          ? t('privacy.safeMode.activeDescription')
          : t('privacy.safeMode.inactiveDescription')
        }
      </Text>
    </View>
  );

  const renderSettingItem = (item: PrivacySettingItem) => (
    <View key={item.id} style={styles.settingItem}>
      <View style={styles.settingContent}>
        <View style={styles.settingHeader}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          {item.critical && (
            <Icon name="warning" size={16} color="#FF9800" />
          )}
        </View>
        <Text style={styles.settingDescription}>{item.description}</Text>
        {item.requiresRestart && (
          <Text style={styles.restartNote}>{t('privacy.settings.requiresRestart')}</Text>
        )}
      </View>
      <Switch
        value={item.value}
        onValueChange={(value) => updatePrivacySetting(item.id, value)}
        disabled={saving}
      />
    </View>
  );

  const renderDataCategoryItem = (category: DataCategoryItem) => (
    <View key={category.id} style={styles.categoryItem}>
      <View style={styles.categoryContent}>
        <Text style={styles.categoryTitle}>{category.nameAr}</Text>
        <Text style={styles.categoryDescription}>{category.descriptionAr}</Text>
        <View style={styles.categoryTags}>
          {category.sensitive && (
            <View style={[styles.tag, styles.sensitiveTag]}>
              <Text style={styles.tagText}>{t('privacy.categories.sensitive')}</Text>
            </View>
          )}
          {category.localOnly && (
            <View style={[styles.tag, styles.localTag]}>
              <Text style={styles.tagText}>{t('privacy.categories.localOnly')}</Text>
            </View>
          )}
        </View>
      </View>
      <View style={styles.categoryStatus}>
        <Text style={[
          styles.statusText,
          { color: category.canShare ? '#4CAF50' : '#F44336' }
        ]}>
          {category.canShare ? t('privacy.categories.canShare') : t('privacy.categories.localOnly')}
        </Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>{t('privacy.loading')}</Text>
      </View>
    );
  }

  const settingItems = getPrivacySettingItems();
  const groupedSettings = {
    data: settingItems.filter(item => item.category === 'data'),
    ai: settingItems.filter(item => item.category === 'ai'),
    sharing: settingItems.filter(item => item.category === 'sharing'),
    security: settingItems.filter(item => item.category === 'security'),
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Privacy Score */}
      {renderPrivacyScore()}

      {/* Safe Mode */}
      {renderSafeModeCard()}

      {/* Data Collection Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('privacy.sections.dataCollection')}</Text>
        {groupedSettings.data.map(renderSettingItem)}
      </View>

      {/* AI Processing Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('privacy.sections.aiProcessing')}</Text>
        {groupedSettings.ai.map(renderSettingItem)}
      </View>

      {/* Data Sharing Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('privacy.sections.dataSharing')}</Text>
        {groupedSettings.sharing.map(renderSettingItem)}
      </View>

      {/* Security Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('privacy.sections.security')}</Text>
        {groupedSettings.security.map(renderSettingItem)}
      </View>

      {/* Data Categories */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('privacy.sections.dataCategories')}</Text>
        {dataCategories.map(renderDataCategoryItem)}
      </View>

      {/* Actions */}
      <View style={styles.actionsSection}>
        <TouchableOpacity 
          style={styles.actionButton} 
          onPress={exportPrivacySettings}
          disabled={saving}
        >
          <Icon name="file-download" size={20} color="#2196F3" />
          <Text style={styles.actionButtonText}>{t('privacy.actions.export')}</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.actionButton, styles.dangerButton]} 
          onPress={resetToDefaults}
          disabled={saving}
        >
          <Icon name="restore" size={20} color="#F44336" />
          <Text style={[styles.actionButtonText, styles.dangerText]}>
            {t('privacy.actions.reset')}
          </Text>
        </TouchableOpacity>
      </View>

      {saving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="small" color="#2196F3" />
          <Text style={styles.savingText}>{t('privacy.saving')}</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  scoreCard: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#333',
  },
  scoreContent: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  scoreDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  safeModeCard: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  safeModeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  safeModeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginLeft: 8,
  },
  safeModeDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingContent: {
    flex: 1,
    marginRight: 16,
  },
  settingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  restartNote: {
    fontSize: 12,
    color: '#FF9800',
    marginTop: 4,
    fontStyle: 'italic',
  },
  categoryItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  categoryContent: {
    flex: 1,
    marginRight: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  categoryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  sensitiveTag: {
    backgroundColor: '#FFE0E0',
  },
  localTag: {
    backgroundColor: '#E0F0FF',
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  categoryStatus: {
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionsSection: {
    padding: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dangerButton: {
    borderWidth: 1,
    borderColor: '#F44336',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2196F3',
    marginLeft: 8,
  },
  dangerText: {
    color: '#F44336',
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  savingText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#2196F3',
  },
});

export default AdvancedPrivacySettingsScreen;
