{"smartPlanner": {"title": "Smart Planner", "description": "AI-powered planning and scheduling", "features": {"scheduling": "Intelligent Scheduling", "reminders": "Smart Reminders", "optimization": "Time Optimization", "integration": "Calendar Integration"}}, "healthAI": {"title": "Health AI", "description": "Personalized health insights and recommendations", "features": {"tracking": "Health Tracking", "insights": "Health Insights", "recommendations": "Personalized Recommendations", "monitoring": "Continuous Monitoring"}}, "learningAI": {"title": "Learning AI", "description": "Adaptive learning assistance and tutoring", "features": {"tutoring": "AI Tutoring", "progress": "Progress Tracking", "adaptation": "Adaptive Learning", "resources": "Learning Resources"}}, "smartShopping": {"title": "Smart Shopping", "description": "Intelligent shopping assistance and recommendations", "features": {"recommendations": "Product Recommendations", "comparison": "Price Comparison", "deals": "Deal Finder", "lists": "Smart Lists"}}, "chatAI": {"title": "Chat AI", "description": "Conversational AI assistant", "features": {"conversation": "Natural Conversation", "context": "Context Awareness", "multimodal": "Multimodal Input", "personalization": "Personalization"}}, "privacyLocalAI": {"title": "Privacy & Local AI", "description": "Privacy-first AI with local processing", "features": {"encryption": "Data Encryption", "localProcessing": "Local Processing", "privacyControls": "Privacy Controls", "safeMode": "Safe Mode"}}, "cameraAR": {"title": "Camera/AR", "description": "Augmented reality and visual AI", "features": {"objectRecognition": "Object Recognition", "textExtraction": "Text Extraction", "translation": "Visual Translation", "ar": "AR Experiences"}}}