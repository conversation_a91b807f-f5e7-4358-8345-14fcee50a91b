/**
 * RTL Provider Component
 * 
 * Provides RTL support and layout direction management
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { I18nManager } from 'react-native';
import { useLanguage } from '@/locales/hooks/useLanguage';

interface RTLContextType {
  isRTL: boolean;
  textDirection: 'ltr' | 'rtl';
  textAlign: 'left' | 'right';
  flexDirection: 'row' | 'row-reverse';
  layoutDirection: 'ltr' | 'rtl';
}

const RTLContext = createContext<RTLContextType>({
  isRTL: false,
  textDirection: 'ltr',
  textAlign: 'left',
  flexDirection: 'row',
  layoutDirection: 'ltr',
});

export const useRTLContext = () => {
  const context = useContext(RTLContext);
  if (!context) {
    throw new Error('useRTLContext must be used within RTLProvider');
  }
  return context;
};

interface RTLProviderProps {
  children: React.ReactNode;
}

export const RTLProvider: React.FC<RTLProviderProps> = ({ children }) => {
  const { currentLanguageInfo, onLanguageChange } = useLanguage();
  const [isRTL, setIsRTL] = useState(currentLanguageInfo.rtl);

  useEffect(() => {
    // Update RTL state when language changes
    const cleanup = onLanguageChange((language) => {
      const languageInfo = currentLanguageInfo;
      const newIsRTL = languageInfo.rtl;
      
      setIsRTL(newIsRTL);
      
      // Update I18nManager for React Native
      if (I18nManager.isRTL !== newIsRTL) {
        I18nManager.allowRTL(newIsRTL);
        I18nManager.forceRTL(newIsRTL);
        
        // Note: In a real app, you might want to restart the app here
        // for complete RTL support, but for this demo we'll handle it manually
        console.log(`📱 RTL mode ${newIsRTL ? 'enabled' : 'disabled'}`);
      }
    });

    return cleanup;
  }, [currentLanguageInfo, onLanguageChange]);

  const contextValue: RTLContextType = {
    isRTL,
    textDirection: isRTL ? 'rtl' : 'ltr',
    textAlign: isRTL ? 'right' : 'left',
    flexDirection: isRTL ? 'row-reverse' : 'row',
    layoutDirection: isRTL ? 'rtl' : 'ltr',
  };

  return (
    <RTLContext.Provider value={contextValue}>
      {children}
    </RTLContext.Provider>
  );
};

/**
 * HOC for RTL-aware components
 */
export const withRTL = <P extends object>(Component: React.ComponentType<P>) => {
  return React.forwardRef<any, P>((props, ref) => {
    const rtlContext = useRTLContext();
    
    return (
      <Component
        {...props}
        ref={ref}
        rtl={rtlContext}
      />
    );
  });
};

/**
 * RTL-aware View component
 */
interface RTLViewProps {
  children: React.ReactNode;
  style?: any;
  rtlStyle?: any;
  ltrStyle?: any;
  [key: string]: any;
}

export const RTLView: React.FC<RTLViewProps> = ({
  children,
  style,
  rtlStyle,
  ltrStyle,
  ...props
}) => {
  const { isRTL } = useRTLContext();
  
  const combinedStyle = [
    style,
    isRTL ? rtlStyle : ltrStyle,
  ];

  return (
    <View style={combinedStyle} {...props}>
      {children}
    </View>
  );
};

/**
 * RTL-aware Text component
 */
interface RTLTextProps {
  children: React.ReactNode;
  style?: any;
  rtlStyle?: any;
  ltrStyle?: any;
  [key: string]: any;
}

export const RTLText: React.FC<RTLTextProps> = ({
  children,
  style,
  rtlStyle,
  ltrStyle,
  ...props
}) => {
  const { isRTL, textAlign } = useRTLContext();
  
  const combinedStyle = [
    { textAlign },
    style,
    isRTL ? rtlStyle : ltrStyle,
  ];

  return (
    <Text style={combinedStyle} {...props}>
      {children}
    </Text>
  );
};

/**
 * RTL-aware TouchableOpacity component
 */
interface RTLTouchableOpacityProps {
  children: React.ReactNode;
  style?: any;
  rtlStyle?: any;
  ltrStyle?: any;
  [key: string]: any;
}

export const RTLTouchableOpacity: React.FC<RTLTouchableOpacityProps> = ({
  children,
  style,
  rtlStyle,
  ltrStyle,
  ...props
}) => {
  const { isRTL, flexDirection } = useRTLContext();
  
  const combinedStyle = [
    { flexDirection },
    style,
    isRTL ? rtlStyle : ltrStyle,
  ];

  return (
    <TouchableOpacity style={combinedStyle} {...props}>
      {children}
    </TouchableOpacity>
  );
};

/**
 * RTL-aware styles helper
 */
export const createRTLStyles = (styles: any) => {
  const { isRTL } = useRTLContext();
  
  const processStyle = (style: any): any => {
    if (!style || typeof style !== 'object') {
      return style;
    }

    const processedStyle = { ...style };

    // Handle margin
    if (style.marginLeft !== undefined || style.marginRight !== undefined) {
      if (isRTL) {
        const temp = processedStyle.marginLeft;
        processedStyle.marginLeft = processedStyle.marginRight;
        processedStyle.marginRight = temp;
      }
    }

    // Handle padding
    if (style.paddingLeft !== undefined || style.paddingRight !== undefined) {
      if (isRTL) {
        const temp = processedStyle.paddingLeft;
        processedStyle.paddingLeft = processedStyle.paddingRight;
        processedStyle.paddingRight = temp;
      }
    }

    // Handle position
    if (style.left !== undefined || style.right !== undefined) {
      if (isRTL) {
        const temp = processedStyle.left;
        processedStyle.left = processedStyle.right;
        processedStyle.right = temp;
      }
    }

    // Handle border
    if (style.borderLeftWidth !== undefined || style.borderRightWidth !== undefined) {
      if (isRTL) {
        const temp = processedStyle.borderLeftWidth;
        processedStyle.borderLeftWidth = processedStyle.borderRightWidth;
        processedStyle.borderRightWidth = temp;
      }
    }

    if (style.borderLeftColor !== undefined || style.borderRightColor !== undefined) {
      if (isRTL) {
        const temp = processedStyle.borderLeftColor;
        processedStyle.borderLeftColor = processedStyle.borderRightColor;
        processedStyle.borderRightColor = temp;
      }
    }

    // Handle flexDirection
    if (style.flexDirection === 'row' && isRTL) {
      processedStyle.flexDirection = 'row-reverse';
    } else if (style.flexDirection === 'row-reverse' && isRTL) {
      processedStyle.flexDirection = 'row';
    }

    // Handle textAlign
    if (style.textAlign === 'left' && isRTL) {
      processedStyle.textAlign = 'right';
    } else if (style.textAlign === 'right' && isRTL) {
      processedStyle.textAlign = 'left';
    }

    return processedStyle;
  };

  if (Array.isArray(styles)) {
    return styles.map(processStyle);
  }

  return processStyle(styles);
};

export default RTLProvider;
