/**
 * Storage Initializer
 * 
 * Handles initialization and setup of the secure storage system
 */

import { Alert } from 'react-native';
import { storageManager, initializeStorage } from './index';
import { STORAGE_KEYS, DEFAULT_PRIVACY_SETTINGS } from './types';

export class StorageInitializer {
  private static initialized = false;

  /**
   * Initialize storage system with proper error handling
   */
  static async initialize(): Promise<boolean> {
    if (StorageInitializer.initialized) {
      return true;
    }

    try {
      console.log('🚀 Starting storage system initialization...');

      // Initialize the storage system
      await initializeStorage();

      // Setup default settings if first run
      await StorageInitializer.setupDefaultSettings();

      // Verify system integrity
      await StorageInitializer.verifySystemIntegrity();

      StorageInitializer.initialized = true;
      console.log('✅ Storage system initialized successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize storage system:', error);
      
      // Show user-friendly error
      Alert.alert(
        'تهيئة النظام',
        'فشل في تهيئة نظام التخزين الآمن. سيتم إعادة المحاولة.',
        [
          {
            text: 'إعادة المحاولة',
            onPress: () => StorageInitializer.initialize(),
          },
          {
            text: 'المتابعة بدون تشفير',
            style: 'destructive',
            onPress: () => StorageInitializer.initializeBasicMode(),
          },
        ]
      );
      
      return false;
    }
  }

  /**
   * Initialize in basic mode (without encryption) as fallback
   */
  static async initializeBasicMode(): Promise<void> {
    try {
      console.log('⚠️ Initializing storage in basic mode (no encryption)');
      
      // Initialize only basic storage without encryption
      const secureStorage = storageManager.getSecureStorage();
      await secureStorage.initialize();
      
      // Set flag to indicate basic mode
      await secureStorage.setItem('storage_mode', 'basic', { encrypt: false });
      
      StorageInitializer.initialized = true;
      console.log('✅ Storage initialized in basic mode');
    } catch (error) {
      console.error('❌ Failed to initialize even basic storage:', error);
      throw error;
    }
  }

  /**
   * Setup default settings for first-time users
   */
  private static async setupDefaultSettings(): Promise<void> {
    try {
      const secureStorage = storageManager.getSecureStorage();
      
      // Check if this is first run
      const existingSettings = await secureStorage.getItem(STORAGE_KEYS.PRIVACY_SETTINGS);
      
      if (!existingSettings) {
        console.log('📋 Setting up default privacy settings...');
        
        // Set default privacy settings
        await secureStorage.setItem(
          STORAGE_KEYS.PRIVACY_SETTINGS,
          DEFAULT_PRIVACY_SETTINGS,
          { encrypt: true }
        );

        // Set default safe mode config
        await secureStorage.setItem(
          STORAGE_KEYS.SAFE_MODE_CONFIG,
          {
            enabled: false,
            allowNetworkAccess: false,
            useLocalAIOnly: true,
            encryptAllData: true,
            disableAnalytics: true,
            disableTelemetry: true,
            maxLocalStorageSize: 100,
            autoEnableOnLowConnectivity: true,
          },
          { encrypt: true }
        );

        // Set onboarding flag
        await secureStorage.setItem(
          STORAGE_KEYS.ONBOARDING_STATE,
          {
            completed: false,
            privacySetupCompleted: false,
            safeModeIntroShown: false,
          },
          { encrypt: false }
        );

        console.log('✅ Default settings configured');
      }
    } catch (error) {
      console.error('Failed to setup default settings:', error);
      throw error;
    }
  }

  /**
   * Verify system integrity and perform health checks
   */
  private static async verifySystemIntegrity(): Promise<void> {
    try {
      console.log('🔍 Verifying storage system integrity...');

      // Test encryption service
      const encryptionService = storageManager.getEncryptionService();
      if (!encryptionService.isInitialized()) {
        throw new Error('Encryption service not initialized');
      }

      // Test basic encryption/decryption
      const testData = 'test_data_' + Date.now();
      const encrypted = await encryptionService.encrypt(testData);
      const decrypted = await encryptionService.decrypt(encrypted);
      
      if (decrypted !== testData) {
        throw new Error('Encryption/decryption test failed');
      }

      // Test secure storage
      const secureStorage = storageManager.getSecureStorage();
      const testKey = 'integrity_test';
      const testValue = { test: true, timestamp: Date.now() };
      
      await secureStorage.setItem(testKey, testValue, { encrypt: true });
      const retrieved = await secureStorage.getItem(testKey);
      await secureStorage.removeItem(testKey);
      
      if (!retrieved || retrieved.test !== true) {
        throw new Error('Secure storage test failed');
      }

      // Test database
      const database = storageManager.getDatabase();
      const stats = await database.getStats();
      
      if (!stats || typeof stats.totalRecords !== 'number') {
        throw new Error('Database test failed');
      }

      console.log('✅ System integrity verified');
    } catch (error) {
      console.error('System integrity check failed:', error);
      throw error;
    }
  }

  /**
   * Perform migration if needed
   */
  static async performMigration(): Promise<void> {
    try {
      const secureStorage = storageManager.getSecureStorage();
      
      // Check current version
      const currentVersion = await secureStorage.getItem('storage_version');
      const targetVersion = '1.0.0';
      
      if (currentVersion !== targetVersion) {
        console.log(`🔄 Migrating storage from ${currentVersion || 'unknown'} to ${targetVersion}`);
        
        // Perform migration steps here
        await StorageInitializer.migrateToV1();
        
        // Update version
        await secureStorage.setItem('storage_version', targetVersion, { encrypt: false });
        
        console.log('✅ Migration completed');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migration to version 1.0.0
   */
  private static async migrateToV1(): Promise<void> {
    // Add migration logic here if needed
    console.log('📦 Migrating to v1.0.0...');
  }

  /**
   * Reset storage system (for testing or emergency)
   */
  static async reset(): Promise<void> {
    try {
      console.log('🔄 Resetting storage system...');
      
      await storageManager.reset();
      StorageInitializer.initialized = false;
      
      console.log('✅ Storage system reset completed');
    } catch (error) {
      console.error('Failed to reset storage system:', error);
      throw error;
    }
  }

  /**
   * Get initialization status
   */
  static isInitialized(): boolean {
    return StorageInitializer.initialized;
  }

  /**
   * Get storage health status
   */
  static async getHealthStatus(): Promise<{
    healthy: boolean;
    issues: string[];
    stats: any;
  }> {
    const issues: string[] = [];
    let healthy = true;

    try {
      // Check if initialized
      if (!StorageInitializer.initialized) {
        issues.push('Storage system not initialized');
        healthy = false;
      }

      // Check encryption service
      const encryptionService = storageManager.getEncryptionService();
      if (!encryptionService.isInitialized()) {
        issues.push('Encryption service not available');
        healthy = false;
      }

      // Check privacy manager
      const privacyManager = storageManager.getPrivacyManager();
      const privacyScore = privacyManager.getPrivacyScore();
      
      if (privacyScore < 50) {
        issues.push('Low privacy score detected');
      }

      // Get statistics
      const stats = await storageManager.getStatistics();

      return {
        healthy,
        issues,
        stats,
      };
    } catch (error) {
      return {
        healthy: false,
        issues: ['Failed to check health status', error.message],
        stats: null,
      };
    }
  }

  /**
   * Cleanup and optimize storage
   */
  static async cleanup(): Promise<void> {
    try {
      console.log('🧹 Starting storage cleanup...');
      
      await storageManager.cleanup();
      
      console.log('✅ Storage cleanup completed');
    } catch (error) {
      console.error('Storage cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Create backup of all data
   */
  static async createBackup(): Promise<string> {
    try {
      console.log('💾 Creating storage backup...');
      
      const backup = await storageManager.exportData();
      
      console.log('✅ Backup created successfully');
      return backup;
    } catch (error) {
      console.error('Backup creation failed:', error);
      throw error;
    }
  }

  /**
   * Restore from backup
   */
  static async restoreFromBackup(backupData: string): Promise<void> {
    try {
      console.log('📥 Restoring from backup...');
      
      await storageManager.importData(backupData);
      
      console.log('✅ Restore completed successfully');
    } catch (error) {
      console.error('Restore failed:', error);
      throw error;
    }
  }

  /**
   * Enable safe mode automatically based on conditions
   */
  static async autoEnableSafeModeIfNeeded(): Promise<void> {
    try {
      const privacyManager = storageManager.getPrivacyManager();
      const safeModeConfig = privacyManager.getSafeModeConfig();
      
      // Auto-enable if configured and offline
      if (safeModeConfig.autoEnableOnLowConnectivity && !privacyManager.isNetworkAvailable()) {
        console.log('📶 Network unavailable, auto-enabling safe mode...');
        await storageManager.enableSafeMode();
      }
    } catch (error) {
      console.error('Failed to auto-enable safe mode:', error);
    }
  }

  /**
   * Setup periodic maintenance tasks
   */
  static setupMaintenanceTasks(): void {
    // Cleanup every 24 hours
    setInterval(async () => {
      try {
        await StorageInitializer.cleanup();
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    }, 24 * 60 * 60 * 1000);

    // Check for auto safe mode every 5 minutes
    setInterval(async () => {
      try {
        await StorageInitializer.autoEnableSafeModeIfNeeded();
      } catch (error) {
        console.error('Auto safe mode check failed:', error);
      }
    }, 5 * 60 * 1000);
  }
}

export default StorageInitializer;
