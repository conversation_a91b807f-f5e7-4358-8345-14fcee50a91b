/**
 * Health & Fitness Screen - صفحة الصحة واللياقة
 * 
 * شاشة متابعة الصحة واللياقة البدنية مع الذكاء الاصطناعي
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Card, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import HealthScoreCard from '../components/health/HealthScoreCard';
import VitalSignsCard from '../components/health/VitalSignsCard';
import ActivitySummaryCard from '../components/health/ActivitySummaryCard';
import HealthInsightsCard from '../components/health/HealthInsightsCard';
import MedicationReminderCard from '../components/health/MedicationReminderCard';
import SymptomTrackerCard from '../components/health/SymptomTrackerCard';
import HealthGoalsCard from '../components/health/HealthGoalsCard';
import QuickHealthActions from '../components/health/QuickHealthActions';

// Hooks
import { useHealthTracking } from '@/features/health-ai/hooks/useHealthTracking';
import { askHealthAI } from '@/services/ai';

const { width } = Dimensions.get('window');

interface HealthMetric {
  id: string;
  type: string;
  value: number;
  unit: string;
  timestamp: Date;
  trend: 'up' | 'down' | 'stable';
}

interface HealthGoal {
  id: string;
  title: string;
  target: number;
  current: number;
  unit: string;
  deadline: Date;
  progress: number;
}

const HealthFitnessScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('day');
  const [healthInsights, setHealthInsights] = useState<any[]>([]);
  const [loadingInsights, setLoadingInsights] = useState(false);

  // Hooks
  const {
    healthScore,
    vitalSigns,
    activityData,
    medications,
    symptoms,
    goals,
    isLoading,
    refreshHealthData,
    addHealthMetric,
    updateGoal,
  } = useHealthTracking();

  // Effects
  useEffect(() => {
    loadHealthInsights();
  }, [vitalSigns, activityData]);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshHealthData();
      await loadHealthInsights();
    } catch (error) {
      console.error('Failed to refresh health data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadHealthInsights = async () => {
    if (!vitalSigns || !activityData) return;

    setLoadingInsights(true);
    try {
      const response = await askHealthAI.suggestHealthTips({
        profile: {
          age: 30, // Get from user profile
          gender: 'male', // Get from user profile
          activity: 'moderate',
        },
        metrics: vitalSigns,
        activity: activityData,
        goals: goals.map(g => g.title),
      });
      setHealthInsights(response.result.tips || []);
    } catch (error) {
      console.error('Failed to load health insights:', error);
    } finally {
      setLoadingInsights(false);
    }
  };

  const handleAddMetric = () => {
    navigation.navigate('AddHealthMetric');
  };

  const handleSymptomCheck = () => {
    navigation.navigate('SymptomChecker');
  };

  const handleMedicationManagement = () => {
    navigation.navigate('MedicationManagement');
  };

  const handleViewHealthHistory = () => {
    navigation.navigate('HealthHistory');
  };

  const handleGoalPress = (goal: HealthGoal) => {
    navigation.navigate('HealthGoalDetails', { goalId: goal.id });
  };

  const handleQuickAction = async (action: string) => {
    switch (action) {
      case 'log_water':
        await addHealthMetric({
          type: 'water_intake',
          value: 250,
          unit: 'ml',
          timestamp: new Date(),
        });
        break;
      case 'log_weight':
        navigation.navigate('LogWeight');
        break;
      case 'log_exercise':
        navigation.navigate('LogExercise');
        break;
      case 'check_symptoms':
        handleSymptomCheck();
        break;
      default:
        break;
    }
  };

  const quickActions = [
    {
      id: 'log_water',
      title: t('health.quickActions.logWater'),
      icon: 'droplet',
      color: theme.colors.info.main,
    },
    {
      id: 'log_weight',
      title: t('health.quickActions.logWeight'),
      icon: 'scale',
      color: theme.colors.warning.main,
    },
    {
      id: 'log_exercise',
      title: t('health.quickActions.logExercise'),
      icon: 'activity',
      color: theme.colors.success.main,
    },
    {
      id: 'check_symptoms',
      title: t('health.quickActions.checkSymptoms'),
      icon: 'stethoscope',
      color: theme.colors.error.main,
    },
  ];

  return (
    <Container padding="none">
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text variant="subtitle1" weight="bold" i18nKey="health.title" />
          <View style={styles.headerActions}>
            <Button
              title=""
              icon="history"
              variant="ghost"
              size="sm"
              onPress={handleViewHealthHistory}
            />
            <Button
              title=""
              icon="plus"
              variant="primary"
              size="sm"
              onPress={handleAddMetric}
            />
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary.main}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Health Score */}
        <View style={styles.section}>
          <HealthScoreCard
            score={healthScore}
            trend="up"
            insights={healthInsights.slice(0, 2)}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <QuickHealthActions
            actions={quickActions}
            onActionPress={handleQuickAction}
          />
        </View>

        {/* Vital Signs */}
        <View style={styles.section}>
          <VitalSignsCard
            vitalSigns={vitalSigns}
            period={selectedPeriod}
            onPeriodChange={setSelectedPeriod}
          />
        </View>

        {/* Activity Summary */}
        <View style={styles.section}>
          <ActivitySummaryCard
            activityData={activityData}
            goals={goals.filter(g => g.title.includes('steps') || g.title.includes('exercise'))}
          />
        </View>

        {/* Health Goals */}
        <View style={styles.section}>
          <HealthGoalsCard
            goals={goals}
            onGoalPress={handleGoalPress}
            onAddGoal={() => navigation.navigate('CreateHealthGoal')}
          />
        </View>

        {/* Medication Reminders */}
        {medications && medications.length > 0 && (
          <View style={styles.section}>
            <MedicationReminderCard
              medications={medications}
              onManageMedications={handleMedicationManagement}
            />
          </View>
        )}

        {/* Symptom Tracker */}
        <View style={styles.section}>
          <SymptomTrackerCard
            recentSymptoms={symptoms}
            onCheckSymptoms={handleSymptomCheck}
            onViewHistory={() => navigation.navigate('SymptomHistory')}
          />
        </View>

        {/* Health Insights */}
        <View style={styles.section}>
          <HealthInsightsCard
            insights={healthInsights}
            loading={loadingInsights}
            onViewMore={() => navigation.navigate('HealthInsights')}
          />
        </View>

        {/* Health Tips */}
        <View style={styles.section}>
          <Card>
            <Text
              variant="subtitle2"
              weight="semibold"
              style={styles.sectionTitle}
              i18nKey="health.dailyTips.title"
            />
            <View style={styles.tipsContainer}>
              <View style={styles.tipItem}>
                <Text variant="body2" i18nKey="health.dailyTips.tip1" />
              </View>
              <View style={styles.tipItem}>
                <Text variant="body2" i18nKey="health.dailyTips.tip2" />
              </View>
              <View style={styles.tipItem}>
                <Text variant="body2" i18nKey="health.dailyTips.tip3" />
              </View>
            </View>
          </Card>
        </View>

        {/* Emergency Actions */}
        <View style={styles.section}>
          <Card style={styles.emergencyCard}>
            <Text
              variant="subtitle2"
              weight="semibold"
              color="error"
              style={styles.emergencyTitle}
              i18nKey="health.emergency.title"
            />
            <Text
              variant="body2"
              color="textSecondary"
              style={styles.emergencyDescription}
              i18nKey="health.emergency.description"
            />
            <Button
              title={t('health.emergency.call')}
              variant="danger"
              icon="phone"
              onPress={() => {
                // Handle emergency call
              }}
              style={styles.emergencyButton}
            />
          </Card>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  tipsContainer: {
    gap: 12,
  },
  tipItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  emergencyCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  emergencyTitle: {
    marginBottom: 8,
  },
  emergencyDescription: {
    marginBottom: 16,
    lineHeight: 20,
  },
  emergencyButton: {
    alignSelf: 'flex-start',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default HealthFitnessScreen;
