/**
 * Safe Mode Screen
 * 
 * Interface for managing safe mode and local AI capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  Alert,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { 
  SafeModeConfig,
  LocalAICapabilities,
  storageManager 
} from '@/core/storage';

interface SafeModeStats {
  knowledgeBaseEntries: number;
  cachedResponses: number;
  capabilities: LocalAICapabilities;
  isActive: boolean;
  isOnline: boolean;
}

const SafeModeScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState<SafeModeConfig | null>(null);
  const [stats, setStats] = useState<SafeModeStats | null>(null);
  const [testInput, setTestInput] = useState('');
  const [testOutput, setTestOutput] = useState('');
  const [testing, setTesting] = useState(false);

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const privacyManager = storageManager.getPrivacyManager();
      const safeModeService = storageManager.getSafeModeService();
      
      const [safeModeConfig, safeModeStats] = await Promise.all([
        privacyManager.getSafeModeConfig(),
        safeModeService.getStatistics(),
      ]);

      setConfig(safeModeConfig);
      setStats(safeModeStats);
    } catch (error) {
      console.error('Failed to load safe mode data:', error);
      Alert.alert(
        t('error.title'),
        t('safeMode.errors.loadFailed')
      );
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async (updates: Partial<SafeModeConfig>) => {
    if (!config) return;

    try {
      setSaving(true);
      
      const newConfig = { ...config, ...updates };
      setConfig(newConfig);

      await storageManager.getPrivacyManager().updateSafeModeConfig(updates);
    } catch (error) {
      console.error('Failed to update safe mode config:', error);
      setConfig(config); // Revert
      Alert.alert(
        t('error.title'),
        t('safeMode.errors.updateFailed')
      );
    } finally {
      setSaving(false);
    }
  };

  const toggleSafeMode = async () => {
    if (!config) return;

    try {
      setSaving(true);
      
      if (config.enabled) {
        await storageManager.disableSafeMode();
      } else {
        await storageManager.enableSafeMode();
      }

      await loadData();
    } catch (error) {
      console.error('Failed to toggle safe mode:', error);
      Alert.alert(
        t('error.title'),
        t('safeMode.errors.toggleFailed')
      );
    } finally {
      setSaving(false);
    }
  };

  const testLocalAI = async () => {
    if (!testInput.trim()) {
      Alert.alert(
        t('safeMode.test.title'),
        t('safeMode.test.enterInput')
      );
      return;
    }

    try {
      setTesting(true);
      setTestOutput('');

      const response = await storageManager.processWithLocalAI(testInput, 'chat');
      
      setTestOutput(response.response);
    } catch (error) {
      console.error('Failed to test local AI:', error);
      setTestOutput(t('safeMode.test.error'));
    } finally {
      setTesting(false);
    }
  };

  const clearCache = async () => {
    Alert.alert(
      t('safeMode.cache.clearTitle'),
      t('safeMode.cache.clearConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('safeMode.cache.clear'),
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              await storageManager.getSafeModeService().clearCache();
              await loadData();
              
              Alert.alert(
                t('safeMode.cache.clearTitle'),
                t('safeMode.cache.cleared')
              );
            } catch (error) {
              console.error('Failed to clear cache:', error);
              Alert.alert(
                t('error.title'),
                t('safeMode.errors.clearCacheFailed')
              );
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };

  const renderStatusCard = () => (
    <View style={styles.statusCard}>
      <View style={styles.statusHeader}>
        <Icon 
          name={config?.enabled ? "shield" : "security"} 
          size={32} 
          color={config?.enabled ? "#4CAF50" : "#FF9800"} 
        />
        <View style={styles.statusContent}>
          <Text style={styles.statusTitle}>
            {config?.enabled ? t('safeMode.status.active') : t('safeMode.status.inactive')}
          </Text>
          <Text style={styles.statusDescription}>
            {config?.enabled 
              ? t('safeMode.status.activeDescription')
              : t('safeMode.status.inactiveDescription')
            }
          </Text>
        </View>
        <Switch
          value={config?.enabled || false}
          onValueChange={toggleSafeMode}
          disabled={saving}
        />
      </View>
      
      {stats && (
        <View style={styles.statusStats}>
          <View style={styles.statItem}>
            <Icon name="wifi-off" size={16} color="#666" />
            <Text style={styles.statText}>
              {stats.isOnline ? t('safeMode.status.online') : t('safeMode.status.offline')}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="storage" size={16} color="#666" />
            <Text style={styles.statText}>
              {t('safeMode.status.knowledgeBase', { count: stats.knowledgeBaseEntries })}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Icon name="cached" size={16} color="#666" />
            <Text style={styles.statText}>
              {t('safeMode.status.cachedResponses', { count: stats.cachedResponses })}
            </Text>
          </View>
        </View>
      )}
    </View>
  );

  const renderCapabilitiesCard = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t('safeMode.capabilities.title')}</Text>
      
      {stats?.capabilities && Object.entries(stats.capabilities).map(([key, available]) => (
        <View key={key} style={styles.capabilityItem}>
          <Icon 
            name={available ? "check-circle" : "cancel"} 
            size={20} 
            color={available ? "#4CAF50" : "#F44336"} 
          />
          <Text style={[
            styles.capabilityText,
            { color: available ? "#333" : "#999" }
          ]}>
            {t(`safeMode.capabilities.${key}`)}
          </Text>
        </View>
      ))}
    </View>
  );

  const renderConfigSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t('safeMode.config.title')}</Text>
      
      <View style={styles.configItem}>
        <View style={styles.configContent}>
          <Text style={styles.configTitle}>{t('safeMode.config.allowNetworkAccess')}</Text>
          <Text style={styles.configDescription}>
            {t('safeMode.config.allowNetworkAccessDesc')}
          </Text>
        </View>
        <Switch
          value={config?.allowNetworkAccess || false}
          onValueChange={(value) => updateConfig({ allowNetworkAccess: value })}
          disabled={saving || config?.enabled}
        />
      </View>

      <View style={styles.configItem}>
        <View style={styles.configContent}>
          <Text style={styles.configTitle}>{t('safeMode.config.autoEnable')}</Text>
          <Text style={styles.configDescription}>
            {t('safeMode.config.autoEnableDesc')}
          </Text>
        </View>
        <Switch
          value={config?.autoEnableOnLowConnectivity || false}
          onValueChange={(value) => updateConfig({ autoEnableOnLowConnectivity: value })}
          disabled={saving}
        />
      </View>

      <View style={styles.configItem}>
        <View style={styles.configContent}>
          <Text style={styles.configTitle}>{t('safeMode.config.encryptAllData')}</Text>
          <Text style={styles.configDescription}>
            {t('safeMode.config.encryptAllDataDesc')}
          </Text>
        </View>
        <Switch
          value={config?.encryptAllData || false}
          onValueChange={(value) => updateConfig({ encryptAllData: value })}
          disabled={saving}
        />
      </View>

      <View style={styles.configItem}>
        <View style={styles.configContent}>
          <Text style={styles.configTitle}>{t('safeMode.config.disableAnalytics')}</Text>
          <Text style={styles.configDescription}>
            {t('safeMode.config.disableAnalyticsDesc')}
          </Text>
        </View>
        <Switch
          value={config?.disableAnalytics || false}
          onValueChange={(value) => updateConfig({ disableAnalytics: value })}
          disabled={saving}
        />
      </View>
    </View>
  );

  const renderTestSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t('safeMode.test.title')}</Text>
      
      <View style={styles.testContainer}>
        <TextInput
          style={styles.testInput}
          placeholder={t('safeMode.test.placeholder')}
          value={testInput}
          onChangeText={setTestInput}
          multiline
          numberOfLines={3}
          editable={!testing}
        />
        
        <TouchableOpacity
          style={[styles.testButton, testing && styles.testButtonDisabled]}
          onPress={testLocalAI}
          disabled={testing || !config?.enabled}
        >
          {testing ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Icon name="send" size={20} color="#fff" />
          )}
          <Text style={styles.testButtonText}>
            {testing ? t('safeMode.test.testing') : t('safeMode.test.test')}
          </Text>
        </TouchableOpacity>

        {testOutput ? (
          <View style={styles.testOutput}>
            <Text style={styles.testOutputLabel}>{t('safeMode.test.response')}:</Text>
            <Text style={styles.testOutputText}>{testOutput}</Text>
          </View>
        ) : null}
      </View>
    </View>
  );

  const renderActionsSection = () => (
    <View style={styles.actionsSection}>
      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={clearCache}
        disabled={saving}
      >
        <Icon name="clear-all" size={20} color="#FF9800" />
        <Text style={[styles.actionButtonText, { color: '#FF9800' }]}>
          {t('safeMode.actions.clearCache')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.actionButton} 
        onPress={() => navigation.navigate('AdvancedPrivacySettings')}
        disabled={saving}
      >
        <Icon name="settings" size={20} color="#2196F3" />
        <Text style={styles.actionButtonText}>
          {t('safeMode.actions.privacySettings')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>{t('safeMode.loading')}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderStatusCard()}
      {renderCapabilitiesCard()}
      {renderConfigSection()}
      {renderTestSection()}
      {renderActionsSection()}

      {saving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="small" color="#2196F3" />
          <Text style={styles.savingText}>{t('safeMode.saving')}</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  statusCard: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusContent: {
    flex: 1,
    marginLeft: 12,
  },
  statusTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  statusDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  statusStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  capabilityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  capabilityText: {
    fontSize: 16,
    marginLeft: 12,
  },
  configItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  configContent: {
    flex: 1,
    marginRight: 16,
  },
  configTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  configDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  testContainer: {
    padding: 16,
  },
  testInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  testButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  testButtonDisabled: {
    backgroundColor: '#ccc',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  testOutput: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  testOutputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  testOutputText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionsSection: {
    padding: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2196F3',
    marginLeft: 8,
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  savingText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#2196F3',
  },
});

export default SafeModeScreen;
