/**
 * UI Components Library - Main Export
 * 
 * مكتبة مكونات واجهة المستخدم الأساسية
 * تصميم نظيف وقابل لإعادة الاستخدام
 */

// Layout Components
export { default as Container } from './layout/Container';
export { default as Card } from './layout/Card';
export { default as Section } from './layout/Section';
export { default as Divider } from './layout/Divider';
export { default as Spacer } from './layout/Spacer';

// Form Components
export { default as Button } from './form/Button';
export { default as Input } from './form/Input';
export { default as TextArea } from './form/TextArea';
export { default as Switch } from './form/Switch';
export { default as Slider } from './form/Slider';
export { default as Checkbox } from './form/Checkbox';

// Display Components
export { default as Text } from './display/Text';
export { default as Heading } from './display/Heading';
export { default as Avatar } from './display/Avatar';
export { default as Badge } from './display/Badge';
export { default as Progress } from './display/Progress';
export { default as Icon } from './display/Icon';

// Feedback Components
export { default as Loading } from './feedback/Loading';
export { default as Toast } from './feedback/Toast';
export { default as Modal } from './feedback/Modal';
export { default as Alert } from './feedback/Alert';
export { default as Skeleton } from './feedback/Skeleton';

// Navigation Components
export { default as TabBar } from './navigation/TabBar';
export { default as Header } from './navigation/Header';
export { default as BackButton } from './navigation/BackButton';

// Data Display Components
export { default as List } from './data/List';
export { default as ListItem } from './data/ListItem';
export { default as Chart } from './data/Chart';
export { default as Table } from './data/Table';

// Utility Components
export { default as SafeArea } from './utility/SafeArea';
export { default as KeyboardAvoidingView } from './utility/KeyboardAvoidingView';
export { default as RefreshControl } from './utility/RefreshControl';

// Types
export type { ButtonProps } from './form/Button';
export type { InputProps } from './form/Input';
export type { TextProps } from './display/Text';
export type { CardProps } from './layout/Card';
export type { ModalProps } from './feedback/Modal';

/**
 * Design System Constants
 */
export const DESIGN_TOKENS = {
  // Spacing
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  // Border Radius
  radius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999,
  },
  
  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  
  // Font Weights
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
  },
  
  // Shadows
  shadow: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 5,
    },
  },
  
  // Animation Durations
  animation: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
} as const;
