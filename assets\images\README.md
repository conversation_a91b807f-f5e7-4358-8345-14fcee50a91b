# App Icons and Assets

## Required Assets for App Store and Google Play

### App Icons

#### iOS Icons (Required)
- `icon-ios.png` - 1024x1024px (App Store icon)
- `icon-20.png` - 20x20px
- `icon-29.png` - 29x29px
- `icon-40.png` - 40x40px
- `icon-58.png` - 58x58px
- `icon-60.png` - 60x60px
- `icon-76.png` - 76x76px
- `icon-80.png` - 80x80px
- `icon-87.png` - 87x87px
- `icon-120.png` - 120x120px
- `icon-152.png` - 152x152px
- `icon-167.png` - 167x167px
- `icon-180.png` - 180x180px

#### Android Icons (Required)
- `icon.png` - 1024x1024px (Main icon)
- `adaptive-icon.png` - 1024x1024px (Adaptive icon foreground)
- `icon-48.png` - 48x48px (mdpi)
- `icon-72.png` - 72x72px (hdpi)
- `icon-96.png` - 96x96px (xhdpi)
- `icon-144.png` - 144x144px (xxhdpi)
- `icon-192.png` - 192x192px (xxxhdpi)

### Splash Screens
- `splash.png` - 1242x2436px (Main splash screen)
- `splash-ios.png` - 1242x2436px (iOS specific)
- `splash-android.png` - 1242x2436px (Android specific)

### Other Assets
- `favicon.png` - 32x32px (Web favicon)
- `logo.png` - 512x512px (General logo)

## Design Guidelines

### Icon Design
- Use the LifeAI Assistant brand colors
- Primary: #2196F3 (Blue)
- Secondary: #4CAF50 (Green)
- Accent: #FF9800 (Orange)
- Background: #FFFFFF (White)

### Icon Content
- Central AI brain/circuit symbol
- Modern, clean design
- Readable at small sizes
- Consistent with brand identity

### Splash Screen
- White background
- Centered logo
- Loading indicator
- Minimal text

## File Structure
```
assets/
├── images/
│   ├── icon.png (1024x1024)
│   ├── icon-ios.png (1024x1024)
│   ├── adaptive-icon.png (1024x1024)
│   ├── splash.png (1242x2436)
│   ├── favicon.png (32x32)
│   └── ios/
│       ├── icon-20.png
│       ├── icon-29.png
│       ├── icon-40.png
│       ├── icon-58.png
│       ├── icon-60.png
│       ├── icon-76.png
│       ├── icon-80.png
│       ├── icon-87.png
│       ├── icon-120.png
│       ├── icon-152.png
│       ├── icon-167.png
│       └── icon-180.png
│   └── android/
│       ├── icon-48.png
│       ├── icon-72.png
│       ├── icon-96.png
│       ├── icon-144.png
│       └── icon-192.png
└── fonts/
    ├── Cairo-Regular.ttf
    ├── Cairo-Bold.ttf
    ├── Roboto-Regular.ttf
    └── Roboto-Bold.ttf
```

## Generation Commands

### Using Expo CLI
```bash
# Generate all icon sizes
npx expo install @expo/image-utils
npx expo generate-icons

# Generate splash screens
npx expo generate-splash-screens
```

### Using ImageMagick (Manual)
```bash
# Generate iOS icons from 1024x1024 source
convert icon-source.png -resize 20x20 icon-20.png
convert icon-source.png -resize 29x29 icon-29.png
convert icon-source.png -resize 40x40 icon-40.png
# ... continue for all sizes

# Generate Android icons from 1024x1024 source
convert icon-source.png -resize 48x48 icon-48.png
convert icon-source.png -resize 72x72 icon-72.png
convert icon-source.png -resize 96x96 icon-96.png
# ... continue for all sizes
```

## Notes
- All icons should be PNG format
- Use transparent backgrounds for adaptive icons
- Test icons on different backgrounds
- Ensure icons are crisp at all sizes
- Follow platform-specific guidelines
