{"general": {"unknown": "An unknown error occurred", "network": "Network error. Please check your connection", "server": "Server error. Please try again later", "timeout": "Request timed out. Please try again", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Resource not found", "conflict": "Conflict occurred", "validation": "Validation error", "maintenance": "Service under maintenance"}, "auth": {"invalidCredentials": "Invalid email or password", "emailExists": "Email already exists", "emailNotFound": "<PERSON><PERSON> not found", "weakPassword": "Password is too weak", "accountDisabled": "Account has been disabled", "tooManyAttempts": "Too many failed attempts", "sessionExpired": "Session has expired", "tokenInvalid": "Invalid token", "biometricFailed": "Biometric authentication failed", "biometricNotAvailable": "Biometric authentication not available"}, "storage": {"saveFailed": "Failed to save data", "loadFailed": "Failed to load data", "deleteFailed": "Failed to delete data", "encryptionFailed": "Encryption failed", "decryptionFailed": "Decryption failed", "storageQuotaExceeded": "Storage quota exceeded", "corruptedData": "Data is corrupted", "keyNotFound": "Encryption key not found", "accessDenied": "Storage access denied"}, "ai": {"processingFailed": "AI processing failed", "modelNotAvailable": "AI model not available", "quotaExceeded": "AI quota exceeded", "invalidInput": "Invalid input for AI processing", "safeModeRequired": "Safe mode required for this operation", "localAINotAvailable": "Local AI not available", "responseTimeout": "AI response timeout", "contextTooLong": "Context too long for processing"}, "privacy": {"permissionDenied": "Permission denied", "dataNotAllowed": "Data sharing not allowed", "encryptionRequired": "Encryption required", "safeModeActive": "Safe mode is active", "privacyViolation": "Privacy policy violation", "consentRequired": "User consent required", "dataRetentionExpired": "Data retention period expired"}, "camera": {"permissionDenied": "Camera permission denied", "notAvailable": "Camera not available", "captureFailed": "Failed to capture image", "processingFailed": "Image processing failed", "formatNotSupported": "Image format not supported", "fileTooLarge": "Image file too large"}, "file": {"uploadFailed": "File upload failed", "downloadFailed": "File download failed", "formatNotSupported": "File format not supported", "fileTooLarge": "File too large", "fileNotFound": "File not found", "accessDenied": "File access denied", "corruptedFile": "File is corrupted"}, "sync": {"syncFailed": "Synchronization failed", "conflictDetected": "Sync conflict detected", "dataOutOfSync": "Data out of sync", "serverUnavailable": "Sync server unavailable", "quotaExceeded": "Sync quota exceeded"}, "actions": {"retry": "Retry", "dismiss": "<PERSON><PERSON><PERSON>", "reportBug": "Report Bug", "contactSupport": "Contact Support", "goBack": "Go Back", "refresh": "Refresh"}}