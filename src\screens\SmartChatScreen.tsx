/**
 * Smart Chat Screen - صفحة الدردشة الذكية
 * 
 * شاشة المحادثة مع الذكاء الاصطناعي المتقدم
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import MessageBubble from '../components/chat/MessageBubble';
import ChatInput from '../components/chat/ChatInput';
import TypingIndicator from '../components/chat/TypingIndicator';
import ChatHeader from '../components/chat/ChatHeader';
import PersonalitySelector from '../components/chat/PersonalitySelector';
import QuickSuggestions from '../components/chat/QuickSuggestions';
import VoiceRecorder from '../components/chat/VoiceRecorder';

// Hooks
import { useChat } from '@/features/chat-ai/hooks/useChat';
import { chatAI } from '@/services/ai';

// Types
interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  type: 'text' | 'voice' | 'image';
  metadata?: any;
}

interface ChatPersonality {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

const SmartChatScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  
  // Refs
  const flatListRef = useRef<FlatList>(null);
  
  // State
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPersonality, setCurrentPersonality] = useState<ChatPersonality>({
    id: 'friendly',
    name: t('chat.personalities.friendly'),
    description: t('chat.personalities.friendlyDesc'),
    icon: 'smile',
    color: theme.colors.primary.main,
  });
  const [showPersonalitySelector, setShowPersonalitySelector] = useState(false);
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [conversationId] = useState(() => `chat_${Date.now()}`);

  // Hooks
  const {
    sendMessage,
    sendStreamingMessage,
    isLoading,
    error,
  } = useChat();

  // Available personalities
  const personalities: ChatPersonality[] = [
    {
      id: 'friendly',
      name: t('chat.personalities.friendly'),
      description: t('chat.personalities.friendlyDesc'),
      icon: 'smile',
      color: theme.colors.primary.main,
    },
    {
      id: 'professional',
      name: t('chat.personalities.professional'),
      description: t('chat.personalities.professionalDesc'),
      icon: 'briefcase',
      color: theme.colors.secondary.main,
    },
    {
      id: 'casual',
      name: t('chat.personalities.casual'),
      description: t('chat.personalities.casualDesc'),
      icon: 'coffee',
      color: theme.colors.warning.main,
    },
    {
      id: 'wise',
      name: t('chat.personalities.wise'),
      description: t('chat.personalities.wiseDesc'),
      icon: 'book',
      color: theme.colors.info.main,
    },
  ];

  // Quick suggestions
  const quickSuggestions = [
    t('chat.suggestions.help'),
    t('chat.suggestions.plan'),
    t('chat.suggestions.learn'),
    t('chat.suggestions.health'),
  ];

  // Effects
  useEffect(() => {
    // Add welcome message
    addMessage({
      id: 'welcome',
      content: t('chat.welcomeMessage'),
      role: 'assistant',
      timestamp: new Date(),
      type: 'text',
    });
  }, []);

  useEffect(() => {
    // Scroll to bottom when new message is added
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Handlers
  const addMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
  };

  const updateLastMessage = (content: string) => {
    setMessages(prev => {
      const updated = [...prev];
      if (updated.length > 0 && updated[updated.length - 1].role === 'assistant') {
        updated[updated.length - 1] = {
          ...updated[updated.length - 1],
          content,
        };
      }
      return updated;
    });
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      content: text.trim(),
      role: 'user',
      timestamp: new Date(),
      type: 'text',
    };
    addMessage(userMessage);
    setInputText('');
    setIsTyping(true);

    try {
      // Add empty assistant message for streaming
      const assistantMessage: Message = {
        id: `assistant_${Date.now()}`,
        content: '',
        role: 'assistant',
        timestamp: new Date(),
        type: 'text',
      };
      addMessage(assistantMessage);
      setIsStreaming(true);

      // Send streaming message
      const streamGenerator = chatAI.sendStreamingMessage({
        message: text.trim(),
        conversationId,
        personality: currentPersonality.id,
        options: {
          style: 'friendly',
          includeEmoji: true,
        },
      });

      let fullResponse = '';
      for await (const chunk of streamGenerator) {
        fullResponse += chunk.delta;
        updateLastMessage(fullResponse);
        
        if (chunk.isComplete) {
          break;
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      Alert.alert(
        t('chat.error.title'),
        t('chat.error.sendFailed'),
        [{ text: t('common.ok') }]
      );
      
      // Remove the empty assistant message on error
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsTyping(false);
      setIsStreaming(false);
    }
  };

  const handleVoiceMessage = async (audioPath: string) => {
    // Handle voice message processing
    console.log('Voice message:', audioPath);
    // TODO: Implement voice-to-text conversion
  };

  const handlePersonalityChange = async (personality: ChatPersonality) => {
    setCurrentPersonality(personality);
    setShowPersonalitySelector(false);
    
    try {
      await chatAI.switchPersonality({
        conversationId,
        newPersonality: personality.id,
      });
      
      // Add system message about personality change
      addMessage({
        id: `system_${Date.now()}`,
        content: t('chat.personalityChanged', { personality: personality.name }),
        role: 'assistant',
        timestamp: new Date(),
        type: 'text',
        metadata: { isSystem: true },
      });
    } catch (error) {
      console.error('Failed to switch personality:', error);
    }
  };

  const handleSuggestionPress = (suggestion: string) => {
    setInputText(suggestion);
  };

  const handleClearChat = () => {
    Alert.alert(
      t('chat.clearChat.title'),
      t('chat.clearChat.message'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.clear'),
          style: 'destructive',
          onPress: () => {
            setMessages([]);
            // Add welcome message again
            setTimeout(() => {
              addMessage({
                id: 'welcome_new',
                content: t('chat.welcomeMessage'),
                role: 'assistant',
                timestamp: new Date(),
                type: 'text',
              });
            }, 100);
          },
        },
      ]
    );
  };

  const renderMessage = ({ item }: { item: Message }) => (
    <MessageBubble
      message={item}
      isStreaming={isStreaming && item === messages[messages.length - 1]}
    />
  );

  return (
    <Container padding="none">
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Header */}
        <ChatHeader
          personality={currentPersonality}
          onPersonalityPress={() => setShowPersonalitySelector(true)}
          onClearChat={handleClearChat}
          onSettings={() => navigation.navigate('ChatSettings')}
        />

        {/* Messages */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }}
        />

        {/* Typing Indicator */}
        {isTyping && <TypingIndicator />}

        {/* Quick Suggestions */}
        {messages.length <= 1 && (
          <QuickSuggestions
            suggestions={quickSuggestions}
            onSuggestionPress={handleSuggestionPress}
          />
        )}

        {/* Chat Input */}
        <ChatInput
          value={inputText}
          onChangeText={setInputText}
          onSend={handleSendMessage}
          onVoicePress={() => setShowVoiceRecorder(true)}
          disabled={isLoading || isTyping}
          placeholder={t('chat.inputPlaceholder')}
        />

        {/* Personality Selector Modal */}
        {showPersonalitySelector && (
          <PersonalitySelector
            personalities={personalities}
            currentPersonality={currentPersonality}
            onSelect={handlePersonalityChange}
            onClose={() => setShowPersonalitySelector(false)}
          />
        )}

        {/* Voice Recorder Modal */}
        {showVoiceRecorder && (
          <VoiceRecorder
            onRecordingComplete={handleVoiceMessage}
            onClose={() => setShowVoiceRecorder(false)}
          />
        )}
      </KeyboardAvoidingView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});

export default SmartChatScreen;
