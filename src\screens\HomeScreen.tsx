/**
 * Home Screen - شاشة البداية
 * 
 * الشاشة الرئيسية للتطبيق مع نظرة عامة على جميع الميزات
 */

import React, { useEffect, useState } from 'react';
import {
  ScrollView,
  View,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Card, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import QuickStatsCard from '../components/home/<USER>';
import FeatureCard from '../components/home/<USER>';
import WelcomeHeader from '../components/home/<USER>';
import AIInsightsCard from '../components/home/<USER>';
import RecentActivityCard from '../components/home/<USER>';

// Hooks
import { useUserProfile } from '@/features/profile/hooks/useUserProfile';

const { width } = Dimensions.get('window');

interface FeatureItem {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
  route: string;
  badge?: string;
}

const HomeScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();
  
  // State
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Hooks
  const { profile, isLoading } = useUserProfile();

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Features configuration
  const features: FeatureItem[] = [
    {
      id: 'smart-planner',
      title: t('features.smartPlanner.title'),
      subtitle: t('features.smartPlanner.subtitle'),
      icon: 'calendar-clock',
      color: theme.colors.primary.main,
      route: 'SmartPlannerScreen',
      badge: '3', // Active tasks count
    },
    {
      id: 'health-ai',
      title: t('features.healthAI.title'),
      subtitle: t('features.healthAI.subtitle'),
      icon: 'heart-pulse',
      color: theme.colors.error.main,
      route: 'HealthDashboardScreen',
    },
    {
      id: 'learning-ai',
      title: t('features.learningAI.title'),
      subtitle: t('features.learningAI.subtitle'),
      icon: 'brain',
      color: theme.colors.secondary.main,
      route: 'LearningDashboardScreen',
      badge: 'New',
    },
    {
      id: 'smart-shopping',
      title: t('features.smartShopping.title'),
      subtitle: t('features.smartShopping.subtitle'),
      icon: 'shopping-cart',
      color: theme.colors.warning.main,
      route: 'ShoppingDashboardScreen',
    },
    {
      id: 'chat-ai',
      title: t('features.chatAI.title'),
      subtitle: t('features.chatAI.subtitle'),
      icon: 'message-circle',
      color: theme.colors.success.main,
      route: 'ChatDashboardScreen',
    },
    {
      id: 'privacy-settings',
      title: t('features.privacy.title'),
      subtitle: t('features.privacy.subtitle'),
      icon: 'shield-check',
      color: theme.colors.info.main,
      route: 'PrivacyDashboardScreen',
    },
  ];

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    // Refresh data from all services
    try {
      // Add refresh logic here
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleFeaturePress = (feature: FeatureItem) => {
    navigation.navigate(feature.route as never);
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return t('home.greeting.morning');
    if (hour < 17) return t('home.greeting.afternoon');
    return t('home.greeting.evening');
  };

  return (
    <Container padding="none">
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary.main}
          />
        }
      >
        {/* Welcome Header */}
        <WelcomeHeader
          greeting={getGreeting()}
          userName={profile?.name || t('home.defaultUserName')}
          currentTime={currentTime}
        />

        {/* Quick Stats */}
        <View style={styles.section}>
          <QuickStatsCard />
        </View>

        {/* AI Insights */}
        <View style={styles.section}>
          <AIInsightsCard />
        </View>

        {/* Features Grid */}
        <View style={styles.section}>
          <Text
            variant="subtitle1"
            weight="semibold"
            style={styles.sectionTitle}
            i18nKey="home.features.title"
          />
          
          <View style={styles.featuresGrid}>
            {features.map((feature, index) => (
              <FeatureCard
                key={feature.id}
                feature={feature}
                onPress={() => handleFeaturePress(feature)}
                style={[
                  styles.featureCard,
                  index % 2 === 0 ? styles.featureCardLeft : styles.featureCardRight,
                ]}
              />
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <RecentActivityCard />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text
            variant="subtitle1"
            weight="semibold"
            style={styles.sectionTitle}
            i18nKey="home.quickActions.title"
          />
          
          <View style={styles.quickActions}>
            <Button
              title={t('home.quickActions.newTask')}
              icon="plus"
              variant="primary"
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('CreateTask' as never)}
            />
            <Button
              title={t('home.quickActions.startChat')}
              icon="message-circle"
              variant="outline"
              style={styles.quickActionButton}
              onPress={() => navigation.navigate('ChatScreen' as never)}
            />
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  featureCard: {
    width: (width - 52) / 2, // Account for padding and gap
    marginBottom: 12,
  },
  featureCardLeft: {
    marginRight: 6,
  },
  featureCardRight: {
    marginLeft: 6,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
  },
  bottomSpacing: {
    height: 100, // Space for tab bar
  },
});

export default HomeScreen;
