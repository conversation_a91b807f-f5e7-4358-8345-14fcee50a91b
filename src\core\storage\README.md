# نظام التخزين المحلي المشفر - Secure Local Storage System

## 🔐 نظرة عامة

نظام تخزين محلي متقدم ومشفر مصمم خصيصاً لتطبيق LifeAI Assistant. يوفر حماية شاملة للبيانات الحساسة مع دعم الوضع الآمن للعمل بدون اتصال إنترنت.

## ✨ الميزات الرئيسية

### 🛡️ الأمان والتشفير
- **تشفير AES-256-GCM**: تشفير قوي لجميع البيانات الحساسة
- **إدارة مفاتيح آمنة**: استخدام React Native Keychain لحفظ مفاتيح التشفير
- **مصادقة بيومترية**: دعم بصمة الإصبع والوجه للوصول الآمن
- **تخزين متعدد الطبقات**: MMKV للسرعة + AsyncStorage للنسخ الاحتياطي

### 🔒 الوضع الآمن (Safe Mode)
- **عمل بدون إنترنت**: معالجة محلية كاملة للبيانات
- **ذكاء اصطناعي محلي**: إمكانيات AI محدودة بدون إرسال بيانات خارجياً
- **تفعيل تلقائي**: تشغيل الوضع الآمن عند انقطاع الاتصال
- **قاعدة معرفة محلية**: استجابات مخزنة مسبقاً للأسئلة الشائعة

### 🎛️ إدارة الخصوصية
- **تحكم دقيق**: تحديد ما يتم مشاركته وما يبقى محلياً
- **فئات البيانات**: تصنيف البيانات حسب الحساسية
- **نقاط الخصوصية**: تقييم مستوى الحماية (0-100)
- **سجل الخصوصية**: تتبع جميع عمليات مشاركة البيانات

### 📊 قاعدة البيانات المشفرة
- **SQLite مشفر**: تخزين منظم للبيانات المعقدة
- **نسخ احتياطي آمن**: تصدير واستيراد مشفر للبيانات
- **تنظيف تلقائي**: إزالة البيانات المنتهية الصلاحية
- **إحصائيات مفصلة**: مراقبة استخدام التخزين

## 🏗️ البنية المعمارية

```
src/core/storage/
├── types.ts                    # تعريف الأنواع والواجهات
├── EncryptionService.ts        # خدمة التشفير والفك
├── SecureStorageService.ts     # التخزين الآمن الأساسي
├── DatabaseService.ts          # قاعدة البيانات المشفرة
├── PrivacyManager.ts          # إدارة إعدادات الخصوصية
├── SafeModeService.ts         # خدمة الوضع الآمن
├── StorageInitializer.ts      # تهيئة النظام
└── index.ts                   # الواجهة الرئيسية
```

## 🚀 الاستخدام السريع

### التهيئة الأساسية

```typescript
import { storageManager, initializeStorage } from '@/core/storage';

// تهيئة النظام عند بدء التطبيق
await initializeStorage();

// التحقق من حالة التهيئة
if (storageManager.isInitialized()) {
  console.log('✅ نظام التخزين جاهز');
}
```

### التخزين الآمن

```typescript
// حفظ بيانات مشفرة
await storageManager.store('user_profile', {
  name: 'أحمد محمد',
  email: '<EMAIL>'
}, true); // true = تشفير

// استرجاع البيانات
const profile = await storageManager.retrieve('user_profile');

// حذف البيانات
await storageManager.remove('user_profile');
```

### إدارة الخصوصية

```typescript
// التحقق من إمكانية مشاركة البيانات
const canShare = storageManager.canShareData('chat_history', true);

// تحديث إعدادات الخصوصية
await storageManager.updatePrivacySettings({
  sendToExternalAI: false,
  useLocalAIOnly: true,
  encryptLocalData: true
});

// الحصول على نقاط الخصوصية
const score = storageManager.getPrivacyScore(); // 0-100
```

### الوضع الآمن

```typescript
// تفعيل الوضع الآمن
await storageManager.enableSafeMode();

// معالجة نص بالذكاء الاصطناعي المحلي
const response = await storageManager.processWithLocalAI(
  'مرحباً، كيف حالك؟',
  'chat'
);

// تعطيل الوضع الآمن
await storageManager.disableSafeMode();
```

## 🎯 استخدام React Hooks

### Hook إدارة الخصوصية

```typescript
import { usePrivacySettings } from '@/features/privacy-local-ai/presentation/hooks/usePrivacySettings';

function PrivacySettingsScreen() {
  const {
    privacySettings,
    privacyScore,
    isSafeModeActive,
    updatePrivacySetting,
    enableSafeMode,
    loading
  } = usePrivacySettings();

  if (loading) return <LoadingSpinner />;

  return (
    <View>
      <Text>نقاط الخصوصية: {privacyScore}/100</Text>
      <Switch
        value={privacySettings?.encryptLocalData}
        onValueChange={(value) => 
          updatePrivacySetting('encryptLocalData', value)
        }
      />
    </View>
  );
}
```

### Hook الوضع الآمن

```typescript
import { useSafeMode } from '@/features/privacy-local-ai/presentation/hooks/usePrivacySettings';

function SafeModeScreen() {
  const { stats, clearCache, loading } = useSafeMode();

  return (
    <View>
      <Text>إدخالات قاعدة المعرفة: {stats?.knowledgeBaseEntries}</Text>
      <Text>الاستجابات المخزنة: {stats?.cachedResponses}</Text>
      <Button title="مسح التخزين المؤقت" onPress={clearCache} />
    </View>
  );
}
```

## 🔧 التكوين المتقدم

### إعدادات التشفير

```typescript
import { DEFAULT_ENCRYPTION_CONFIG } from '@/core/storage';

const customConfig = {
  ...DEFAULT_ENCRYPTION_CONFIG,
  algorithm: 'AES-256-GCM',
  keySize: 256,
  iterations: 100000
};
```

### إعدادات الخصوصية الافتراضية

```typescript
import { DEFAULT_PRIVACY_SETTINGS } from '@/core/storage';

const customPrivacySettings = {
  ...DEFAULT_PRIVACY_SETTINGS,
  sendToExternalAI: false,
  useLocalAIOnly: true,
  encryptLocalData: true,
  biometricAuth: true
};
```

## 📱 واجهات المستخدم

### شاشة إعدادات الخصوصية المتقدمة
```typescript
import AdvancedPrivacySettingsScreen from '@/features/privacy-local-ai/presentation/screens/AdvancedPrivacySettingsScreen';
```

### شاشة الوضع الآمن
```typescript
import SafeModeScreen from '@/features/privacy-local-ai/presentation/screens/SafeModeScreen';
```

## 🛠️ إدارة النظام

### النسخ الاحتياطي والاستعادة

```typescript
// إنشاء نسخة احتياطية
const backup = await storageManager.exportData();

// استعادة من نسخة احتياطية
await storageManager.importData(backup);
```

### الصيانة والتنظيف

```typescript
// تنظيف البيانات القديمة
await storageManager.cleanup();

// إعادة تعيين النظام
await storageManager.reset();

// الحصول على إحصائيات
const stats = await storageManager.getStatistics();
```

### مراقبة الصحة

```typescript
import StorageInitializer from '@/core/storage/StorageInitializer';

const healthStatus = await StorageInitializer.getHealthStatus();
console.log('حالة النظام:', healthStatus.healthy ? 'سليم' : 'يحتاج صيانة');
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **فشل التهيئة**
   ```typescript
   // إعادة تهيئة النظام
   await StorageInitializer.reset();
   await StorageInitializer.initialize();
   ```

2. **مشاكل التشفير**
   ```typescript
   // التحقق من حالة خدمة التشفير
   const encryptionService = storageManager.getEncryptionService();
   if (!encryptionService.isInitialized()) {
     await encryptionService.initialize();
   }
   ```

3. **مشاكل الوضع الآمن**
   ```typescript
   // مسح التخزين المؤقت وإعادة التحميل
   const safeModeService = storageManager.getSafeModeService();
   await safeModeService.clearCache();
   ```

## 📊 مراقبة الأداء

### مؤشرات الأداء الرئيسية
- **سرعة التشفير/الفك**: < 100ms للبيانات الصغيرة
- **استهلاك الذاكرة**: < 50MB للتخزين المؤقت
- **حجم قاعدة البيانات**: مراقبة النمو التلقائي
- **نقاط الخصوصية**: الهدف > 80/100

### تسجيل الأحداث
```typescript
// تسجيل أحداث الخصوصية
const privacyManager = storageManager.getPrivacyManager();
await privacyManager.logPrivacyAction(
  'data_shared',
  'chat_history',
  false, // لم يتم المشاركة خارجياً
  { reason: 'safe_mode_active' }
);
```

## 🔮 التطوير المستقبلي

### ميزات مخططة
- [ ] دعم SQLCipher للتشفير المتقدم
- [ ] تزامن آمن بين الأجهزة
- [ ] ذكاء اصطناعي محلي متقدم
- [ ] تحليلات خصوصية في الوقت الفعلي
- [ ] دعم التوقيعات الرقمية

### تحسينات الأداء
- [ ] ضغط البيانات التلقائي
- [ ] فهرسة محسنة لقاعدة البيانات
- [ ] تخزين مؤقت ذكي
- [ ] تحسين استهلاك البطارية

## 📄 الترخيص

هذا النظام جزء من تطبيق LifeAI Assistant ومرخص تحت رخصة MIT.

## 🤝 المساهمة

للمساهمة في تطوير النظام، يرجى اتباع إرشادات المساهمة في المشروع الرئيسي.

---

**ملاحظة**: هذا النظام مصمم لحماية خصوصية المستخدمين وضمان أمان بياناتهم. يرجى اتباع أفضل الممارسات الأمنية عند التطوير والاستخدام.
