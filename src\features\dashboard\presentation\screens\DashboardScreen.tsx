/**
 * Dashboard Screen
 * 
 * Main dashboard with i18n support and RTL layout
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { useLanguage, useRTLStyles, useLanguageFormatting } from '@/locales/hooks/useLanguage';
import LanguageSelector from '@/ui/components/LanguageSelector';

interface QuickAction {
  id: string;
  titleKey: string;
  icon: string;
  color: string;
  onPress: () => void;
}

interface Stat {
  id: string;
  titleKey: string;
  value: number;
  icon: string;
  color: string;
}

const DashboardScreen: React.FC = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const navigation = useNavigation();
  const { currentLanguageInfo } = useLanguage();
  const { isRTL, textAlign, flexDirection } = useRTLStyles();
  const { formatNumber } = useLanguageFormatting();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [userName] = useState('أحمد محمد'); // Example user name

  // Get time of day for greeting
  const getTimeOfDay = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 18) return 'afternoon';
    return 'evening';
  };

  // Quick actions
  const quickActions: QuickAction[] = [
    {
      id: 'chat',
      titleKey: 'quickActions.chat',
      icon: 'chat',
      color: '#2196F3',
      onPress: () => navigation.navigate('Chat'),
    },
    {
      id: 'planner',
      titleKey: 'quickActions.planner',
      icon: 'event',
      color: '#4CAF50',
      onPress: () => navigation.navigate('SmartPlanner'),
    },
    {
      id: 'health',
      titleKey: 'quickActions.health',
      icon: 'favorite',
      color: '#F44336',
      onPress: () => navigation.navigate('HealthAI'),
    },
    {
      id: 'learning',
      titleKey: 'quickActions.learning',
      icon: 'school',
      color: '#FF9800',
      onPress: () => navigation.navigate('LearningAI'),
    },
    {
      id: 'shopping',
      titleKey: 'quickActions.shopping',
      icon: 'shopping-cart',
      color: '#9C27B0',
      onPress: () => navigation.navigate('SmartShopping'),
    },
    {
      id: 'camera',
      titleKey: 'quickActions.camera',
      icon: 'camera-alt',
      color: '#607D8B',
      onPress: () => navigation.navigate('CameraAR'),
    },
  ];

  // Stats
  const stats: Stat[] = [
    {
      id: 'conversations',
      titleKey: 'stats.conversations',
      value: 42,
      icon: 'chat',
      color: '#2196F3',
    },
    {
      id: 'tasks',
      titleKey: 'stats.tasksCompleted',
      value: 18,
      icon: 'check-circle',
      color: '#4CAF50',
    },
    {
      id: 'health',
      titleKey: 'stats.healthScore',
      value: 85,
      icon: 'favorite',
      color: '#F44336',
    },
    {
      id: 'privacy',
      titleKey: 'stats.privacyScore',
      value: 92,
      icon: 'security',
      color: '#FF9800',
    },
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderHeader = () => (
    <View style={[styles.header, { flexDirection }]}>
      <View style={styles.headerContent}>
        <Text style={[styles.greeting, { textAlign }]}>
          {t('welcome.greeting', { 
            timeOfDay: t(`welcome.timeOfDay.${getTimeOfDay()}`),
            name: userName 
          })}
        </Text>
        <Text style={[styles.subtitle, { textAlign }]}>
          {t('welcome.subtitle')}
        </Text>
      </View>
      <LanguageSelector compact />
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { textAlign }]}>
        {t('quickActions.title')}
      </Text>
      <View style={styles.quickActionsGrid}>
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={[styles.quickActionItem, { backgroundColor: action.color + '15' }]}
            onPress={action.onPress}
          >
            <Icon name={action.icon} size={32} color={action.color} />
            <Text style={[styles.quickActionText, { textAlign: 'center' }]}>
              {t(action.titleKey)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStats = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { textAlign }]}>
        {t('stats.title')}
      </Text>
      <View style={styles.statsGrid}>
        {stats.map((stat) => (
          <View key={stat.id} style={styles.statItem}>
            <View style={[styles.statIcon, { backgroundColor: stat.color + '15' }]}>
              <Icon name={stat.icon} size={24} color={stat.color} />
            </View>
            <Text style={[styles.statValue, { textAlign }]}>
              {formatNumber(stat.value)}
            </Text>
            <Text style={[styles.statTitle, { textAlign }]}>
              {t(stat.titleKey)}
            </Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderRecentActivity = () => (
    <View style={styles.section}>
      <View style={[styles.sectionHeader, { flexDirection }]}>
        <Text style={[styles.sectionTitle, { textAlign }]}>
          {t('recentActivity.title')}
        </Text>
        <TouchableOpacity>
          <Text style={styles.viewAllText}>
            {t('recentActivity.viewAll')}
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.emptyState}>
        <Icon name="history" size={48} color="#ccc" />
        <Text style={[styles.emptyStateText, { textAlign: 'center' }]}>
          {t('recentActivity.noActivity')}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderHeader()}
        {renderQuickActions()}
        {renderStats()}
        {renderRecentActivity()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    alignItems: 'center',
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 20,
  },
  sectionHeader: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '48%',
    aspectRatio: 1.2,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginTop: 8,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#999',
    marginTop: 12,
  },
});

export default DashboardScreen;
