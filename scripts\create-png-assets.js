#!/usr/bin/env node

/**
 * Create PNG Assets Script
 * 
 * Creates simple PNG assets for Expo prebuild
 */

const fs = require('fs');
const path = require('path');

// Create assets directory if it doesn't exist
const assetsDir = path.join(__dirname, '..', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

console.log('🎨 Creating simple PNG assets...');

// Create a simple base64 PNG (1x1 blue pixel)
const bluePNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

// Create different sized PNGs by repeating the pattern
function createSimplePNG(width, height, color = '#2196F3') {
  // This is a very basic approach - creates a simple colored square
  // For a real app, you'd want proper PNG generation
  const canvas = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="${width}" height="${height}" fill="${color}"/>
    <circle cx="${width/2}" cy="${height/3}" r="${Math.min(width, height)/6}" fill="white"/>
    <text x="${width/2}" y="${height*2/3}" text-anchor="middle" fill="white" font-size="${Math.min(width, height)/8}" font-family="Arial">AI</text>
  </svg>`;
  
  return canvas;
}

// Create SVG files that can be converted to PNG
const assets = [
  { name: 'icon.svg', width: 1024, height: 1024 },
  { name: 'adaptive-icon.svg', width: 1024, height: 1024 },
  { name: 'splash.svg', width: 1242, height: 2436 },
  { name: 'favicon.svg', width: 32, height: 32 }
];

assets.forEach(asset => {
  const svgContent = createSimplePNG(asset.width, asset.height);
  fs.writeFileSync(path.join(assetsDir, asset.name), svgContent);
  console.log(`✅ Created ${asset.name} (${asset.width}x${asset.height})`);
});

// Create a simple 1x1 PNG for immediate use
const simplePNG = Buffer.from(bluePNG, 'base64');

// Create PNG files by copying the simple PNG
// This is a workaround - in production you'd use proper image generation
const pngAssets = [
  'icon.png',
  'adaptive-icon.png', 
  'splash.png',
  'favicon.png'
];

pngAssets.forEach(asset => {
  fs.writeFileSync(path.join(assetsDir, asset), simplePNG);
  console.log(`📱 Created placeholder ${asset}`);
});

console.log('');
console.log('✅ PNG assets created successfully!');
console.log('📝 Note: These are minimal placeholder PNGs.');
console.log('   For better quality, convert the SVG files to PNG using:');
console.log('   - Online tools: https://convertio.co/svg-png/');
console.log('   - Or any image editor');
console.log('');
console.log('🚀 Ready for expo prebuild!');
