#!/usr/bin/env node

/**
 * Setup for APK Build Script
 * 
 * Automates the setup process for building APK
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up LifeAI Assistant for APK build...\n');

// Step 1: Check if Expo CLI is available
console.log('📦 Step 1: Checking Expo CLI...');
try {
  execSync('npx @expo/cli --version', { stdio: 'pipe' });
  console.log('✅ Expo CLI is available\n');
} catch (error) {
  console.log('⚠️ Expo CLI not found, but will use npx\n');
}

// Step 2: Create assets
console.log('🎨 Step 2: Creating app assets...');
try {
  execSync('node scripts/create-simple-assets.js', { stdio: 'inherit' });
  console.log('✅ Assets created successfully\n');
} catch (error) {
  console.error('❌ Failed to create assets:', error.message);
  process.exit(1);
}

// Step 3: Check app.json
console.log('⚙️ Step 3: Validating app.json...');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  if (appJson.expo && appJson.expo.name && appJson.expo.slug) {
    console.log('✅ app.json is valid\n');
  } else {
    throw new Error('Invalid app.json structure');
  }
} catch (error) {
  console.error('❌ app.json validation failed:', error.message);
  process.exit(1);
}

// Step 4: Check eas.json
console.log('🔧 Step 4: Validating eas.json...');
try {
  const easJson = JSON.parse(fs.readFileSync('eas.json', 'utf8'));
  if (easJson.build && easJson.build.preview) {
    console.log('✅ eas.json is valid\n');
  } else {
    throw new Error('Invalid eas.json structure');
  }
} catch (error) {
  console.error('❌ eas.json validation failed:', error.message);
  process.exit(1);
}

// Step 5: Create instructions
console.log('📋 Step 5: Creating build instructions...');
const instructions = `
🎉 Setup Complete! Your project is ready for APK build.

📱 Next Steps to Build APK:

1. 🔐 Login to Expo:
   npx expo login

2. ⚙️ Configure EAS Build:
   npx eas build:configure

3. 🎨 Convert SVG assets to PNG:
   - Go to: https://convertio.co/svg-png/
   - Convert assets/icon.svg → assets/icon.png (1024x1024)
   - Convert assets/splash.svg → assets/splash.png (1242x2436)
   - Convert assets/adaptive-icon.svg → assets/adaptive-icon.png (1024x1024)

4. 🚀 Build APK:
   npm run build:apk

5. ⏳ Wait for build to complete (10-20 minutes)

6. 📱 Download and install APK on your phone

📖 For detailed instructions, see: BUILD_APK_GUIDE.md

🔗 Useful Links:
- Expo Dashboard: https://expo.dev/
- SVG to PNG Converter: https://convertio.co/svg-png/
- EAS Build Docs: https://docs.expo.dev/build/introduction/

✨ Happy building!
`;

console.log(instructions);

// Save instructions to file
fs.writeFileSync('BUILD_INSTRUCTIONS.txt', instructions);
console.log('📄 Instructions saved to BUILD_INSTRUCTIONS.txt');

console.log('\n🎯 Summary:');
console.log('✅ Assets created');
console.log('✅ Configuration validated');
console.log('✅ Instructions provided');
console.log('\n🚀 Ready to build APK!');
console.log('\n💡 Quick start: npm run build:apk (after expo login)');
