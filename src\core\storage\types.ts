/**
 * Storage Types and Interfaces
 * 
 * Defines types for secure storage, encryption, and privacy settings
 */

export interface EncryptionConfig {
  algorithm: 'AES-256-GCM' | 'AES-256-CBC';
  keySize: 256 | 512;
  ivSize: 16;
  saltSize: 32;
  iterations: number;
}

export interface SecureStorageOptions {
  encrypt?: boolean;
  compression?: boolean;
  ttl?: number; // Time to live in milliseconds
  accessGroup?: string; // iOS Keychain access group
  touchID?: boolean; // Require biometric authentication
  showModal?: boolean; // Show authentication modal
}

export interface StorageItem<T = any> {
  key: string;
  value: T;
  encrypted: boolean;
  timestamp: number;
  ttl?: number;
  metadata?: Record<string, any>;
}

export interface DatabaseSchema {
  version: number;
  tables: DatabaseTable[];
}

export interface DatabaseTable {
  name: string;
  columns: DatabaseColumn[];
  indexes?: DatabaseIndex[];
  encrypted?: boolean;
}

export interface DatabaseColumn {
  name: string;
  type: 'TEXT' | 'INTEGER' | 'REAL' | 'BLOB';
  primaryKey?: boolean;
  notNull?: boolean;
  unique?: boolean;
  defaultValue?: any;
}

export interface DatabaseIndex {
  name: string;
  columns: string[];
  unique?: boolean;
}

export interface PrivacySettings {
  // Data Collection Settings
  dataCollection: boolean;
  analytics: boolean;
  crashReporting: boolean;
  performanceMonitoring: boolean;
  
  // AI Processing Settings
  sendToExternalAI: boolean;
  useLocalAIOnly: boolean;
  shareConversationHistory: boolean;
  personalizedResponses: boolean;
  
  // Data Sharing Settings
  shareWithThirdParties: boolean;
  allowTelemetry: boolean;
  marketingCommunications: boolean;
  
  // Security Settings
  biometricAuth: boolean;
  autoLock: boolean;
  autoLockTimeout: number; // in minutes
  encryptLocalData: boolean;
  secureBackup: boolean;
}

export interface DataCategory {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  sensitive: boolean;
  required: boolean;
  canBeShared: boolean;
  localOnly: boolean;
}

export interface SafeModeConfig {
  enabled: boolean;
  allowNetworkAccess: boolean;
  useLocalAIOnly: boolean;
  encryptAllData: boolean;
  disableAnalytics: boolean;
  disableTelemetry: boolean;
  maxLocalStorageSize: number; // in MB
  autoEnableOnLowConnectivity: boolean;
}

export interface LocalAICapabilities {
  textProcessing: boolean;
  basicQA: boolean;
  languageDetection: boolean;
  sentimentAnalysis: boolean;
  textSummarization: boolean;
  translation: boolean;
  offlineVoiceRecognition: boolean;
}

export interface EncryptedData {
  data: string; // Base64 encoded encrypted data
  iv: string; // Initialization vector
  salt: string; // Salt for key derivation
  algorithm: string;
  timestamp: number;
}

export interface StorageMetrics {
  totalSize: number;
  encryptedSize: number;
  unencryptedSize: number;
  itemCount: number;
  lastCleanup: number;
  compressionRatio: number;
}

export interface BackupData {
  version: string;
  timestamp: number;
  encrypted: boolean;
  data: Record<string, any>;
  checksum: string;
}

// Storage Events
export type StorageEventType = 
  | 'item_added'
  | 'item_updated'
  | 'item_removed'
  | 'storage_cleared'
  | 'encryption_enabled'
  | 'encryption_disabled'
  | 'safe_mode_enabled'
  | 'safe_mode_disabled';

export interface StorageEvent {
  type: StorageEventType;
  key?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export type StorageEventListener = (event: StorageEvent) => void;

// Error Types
export class StorageError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'StorageError';
  }
}

export class EncryptionError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'EncryptionError';
  }
}

// Constants
export const STORAGE_KEYS = {
  // User Data
  USER_PROFILE: 'user_profile',
  USER_PREFERENCES: 'user_preferences',
  PRIVACY_SETTINGS: 'privacy_settings',
  LANGUAGE: 'language',

  // AI Data
  CHAT_HISTORY: 'chat_history',
  AI_PREFERENCES: 'ai_preferences',
  LOCAL_AI_DATA: 'local_ai_data',

  // Security
  ENCRYPTION_KEY: 'encryption_key',
  BIOMETRIC_SETTINGS: 'biometric_settings',
  SAFE_MODE_CONFIG: 'safe_mode_config',

  // App State
  APP_SETTINGS: 'app_settings',
  FEATURE_FLAGS: 'feature_flags',
  ONBOARDING_STATE: 'onboarding_state',
} as const;

export const DATA_CATEGORIES: Record<string, DataCategory> = {
  PERSONAL_INFO: {
    id: 'personal_info',
    name: 'Personal Information',
    nameAr: 'المعلومات الشخصية',
    description: 'Name, email, profile data',
    descriptionAr: 'الاسم، البريد الإلكتروني، بيانات الملف الشخصي',
    sensitive: true,
    required: true,
    canBeShared: false,
    localOnly: true,
  },
  CHAT_HISTORY: {
    id: 'chat_history',
    name: 'Chat History',
    nameAr: 'سجل المحادثات',
    description: 'AI conversation history',
    descriptionAr: 'سجل محادثات الذكاء الاصطناعي',
    sensitive: true,
    required: false,
    canBeShared: true,
    localOnly: false,
  },
  USAGE_ANALYTICS: {
    id: 'usage_analytics',
    name: 'Usage Analytics',
    nameAr: 'تحليلات الاستخدام',
    description: 'App usage patterns and statistics',
    descriptionAr: 'أنماط استخدام التطبيق والإحصائيات',
    sensitive: false,
    required: false,
    canBeShared: true,
    localOnly: false,
  },
  PREFERENCES: {
    id: 'preferences',
    name: 'User Preferences',
    nameAr: 'تفضيلات المستخدم',
    description: 'App settings and preferences',
    descriptionAr: 'إعدادات التطبيق والتفضيلات',
    sensitive: false,
    required: true,
    canBeShared: false,
    localOnly: true,
  },
} as const;

export const DEFAULT_ENCRYPTION_CONFIG: EncryptionConfig = {
  algorithm: 'AES-256-GCM',
  keySize: 256,
  ivSize: 16,
  saltSize: 32,
  iterations: 100000,
};

export const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
  dataCollection: false,
  analytics: false,
  crashReporting: true,
  performanceMonitoring: false,
  sendToExternalAI: false,
  useLocalAIOnly: true,
  shareConversationHistory: false,
  personalizedResponses: true,
  shareWithThirdParties: false,
  allowTelemetry: false,
  marketingCommunications: false,
  biometricAuth: true,
  autoLock: true,
  autoLockTimeout: 5,
  encryptLocalData: true,
  secureBackup: true,
};

export const DEFAULT_SAFE_MODE_CONFIG: SafeModeConfig = {
  enabled: false,
  allowNetworkAccess: false,
  useLocalAIOnly: true,
  encryptAllData: true,
  disableAnalytics: true,
  disableTelemetry: true,
  maxLocalStorageSize: 100, // 100MB
  autoEnableOnLowConnectivity: true,
};
