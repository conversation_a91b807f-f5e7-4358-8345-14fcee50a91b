/**
 * Privacy & Settings Screen - صفحة الخصوصية والإعدادات
 * 
 * شاشة إدارة الخصوصية والإعدادات العامة للتطبيق
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  Switch,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Card, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import SettingsSection from '../components/settings/SettingsSection';
import SettingsItem from '../components/settings/SettingsItem';
import PrivacyScoreCard from '../components/settings/PrivacyScoreCard';
import DataUsageCard from '../components/settings/DataUsageCard';
import SecurityStatusCard from '../components/settings/SecurityStatusCard';
import APIKeyManagement from '../components/settings/APIKeyManagement';
import LocalAISettings from '../components/settings/LocalAISettings';

// Hooks
import { useSettings } from '@/core/settings/useSettings';
import { usePrivacySettings } from '@/features/privacy-local-ai/hooks/usePrivacySettings';
import { AIConfig } from '@/services/ai';

interface SettingsGroup {
  id: string;
  title: string;
  items: SettingItem[];
}

interface SettingItem {
  id: string;
  title: string;
  description?: string;
  type: 'toggle' | 'navigation' | 'action' | 'info';
  value?: boolean | string;
  icon?: string;
  onPress?: () => void;
  onToggle?: (value: boolean) => void;
  danger?: boolean;
}

const PrivacySettingsScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);

  // Hooks
  const {
    settings,
    updateSetting,
    resetSettings,
    exportSettings,
    importSettings,
  } = useSettings();

  const {
    privacyScore,
    dataUsage,
    securityStatus,
    localAIEnabled,
    encryptionEnabled,
    updatePrivacySetting,
  } = usePrivacySettings();

  // Settings configuration
  const settingsGroups: SettingsGroup[] = [
    {
      id: 'privacy',
      title: t('settings.privacy.title'),
      items: [
        {
          id: 'data_collection',
          title: t('settings.privacy.dataCollection'),
          description: t('settings.privacy.dataCollectionDesc'),
          type: 'toggle',
          value: settings.privacy?.dataCollection ?? true,
          onToggle: (value) => updatePrivacySetting('dataCollection', value),
        },
        {
          id: 'analytics',
          title: t('settings.privacy.analytics'),
          description: t('settings.privacy.analyticsDesc'),
          type: 'toggle',
          value: settings.privacy?.analytics ?? false,
          onToggle: (value) => updatePrivacySetting('analytics', value),
        },
        {
          id: 'crash_reports',
          title: t('settings.privacy.crashReports'),
          description: t('settings.privacy.crashReportsDesc'),
          type: 'toggle',
          value: settings.privacy?.crashReports ?? true,
          onToggle: (value) => updatePrivacySetting('crashReports', value),
        },
        {
          id: 'data_management',
          title: t('settings.privacy.dataManagement'),
          description: t('settings.privacy.dataManagementDesc'),
          type: 'navigation',
          icon: 'database',
          onPress: () => navigation.navigate('DataManagement'),
        },
      ],
    },
    {
      id: 'security',
      title: t('settings.security.title'),
      items: [
        {
          id: 'biometric_auth',
          title: t('settings.security.biometricAuth'),
          description: t('settings.security.biometricAuthDesc'),
          type: 'toggle',
          value: settings.security?.biometricAuth ?? false,
          onToggle: (value) => updateSetting('security.biometricAuth', value),
        },
        {
          id: 'encryption',
          title: t('settings.security.encryption'),
          description: t('settings.security.encryptionDesc'),
          type: 'toggle',
          value: encryptionEnabled,
          onToggle: (value) => updatePrivacySetting('encryption', value),
        },
        {
          id: 'auto_lock',
          title: t('settings.security.autoLock'),
          description: t('settings.security.autoLockDesc'),
          type: 'navigation',
          icon: 'lock',
          onPress: () => navigation.navigate('AutoLockSettings'),
        },
        {
          id: 'api_keys',
          title: t('settings.security.apiKeys'),
          description: t('settings.security.apiKeysDesc'),
          type: 'navigation',
          icon: 'key',
          onPress: () => navigation.navigate('APIKeyManagement'),
        },
      ],
    },
    {
      id: 'ai',
      title: t('settings.ai.title'),
      items: [
        {
          id: 'local_ai',
          title: t('settings.ai.localProcessing'),
          description: t('settings.ai.localProcessingDesc'),
          type: 'toggle',
          value: localAIEnabled,
          onToggle: (value) => updatePrivacySetting('localAI', value),
        },
        {
          id: 'ai_providers',
          title: t('settings.ai.providers'),
          description: t('settings.ai.providersDesc'),
          type: 'navigation',
          icon: 'brain',
          onPress: () => navigation.navigate('AIProviderSettings'),
        },
        {
          id: 'ai_memory',
          title: t('settings.ai.memory'),
          description: t('settings.ai.memoryDesc'),
          type: 'navigation',
          icon: 'memory',
          onPress: () => navigation.navigate('AIMemorySettings'),
        },
      ],
    },
    {
      id: 'general',
      title: t('settings.general.title'),
      items: [
        {
          id: 'language',
          title: t('settings.general.language'),
          description: settings.general?.language === 'ar' ? 'العربية' : 'English',
          type: 'navigation',
          icon: 'globe',
          onPress: () => navigation.navigate('LanguageSettings'),
        },
        {
          id: 'theme',
          title: t('settings.general.theme'),
          description: t(`settings.general.themes.${settings.general?.theme || 'system'}`),
          type: 'navigation',
          icon: 'palette',
          onPress: () => navigation.navigate('ThemeSettings'),
        },
        {
          id: 'notifications',
          title: t('settings.general.notifications'),
          description: t('settings.general.notificationsDesc'),
          type: 'navigation',
          icon: 'bell',
          onPress: () => navigation.navigate('NotificationSettings'),
        },
        {
          id: 'backup',
          title: t('settings.general.backup'),
          description: t('settings.general.backupDesc'),
          type: 'navigation',
          icon: 'cloud',
          onPress: () => navigation.navigate('BackupSettings'),
        },
      ],
    },
    {
      id: 'support',
      title: t('settings.support.title'),
      items: [
        {
          id: 'help',
          title: t('settings.support.help'),
          description: t('settings.support.helpDesc'),
          type: 'navigation',
          icon: 'help-circle',
          onPress: () => navigation.navigate('Help'),
        },
        {
          id: 'feedback',
          title: t('settings.support.feedback'),
          description: t('settings.support.feedbackDesc'),
          type: 'navigation',
          icon: 'message-square',
          onPress: () => navigation.navigate('Feedback'),
        },
        {
          id: 'about',
          title: t('settings.support.about'),
          description: t('settings.support.aboutDesc'),
          type: 'navigation',
          icon: 'info',
          onPress: () => navigation.navigate('About'),
        },
      ],
    },
    {
      id: 'danger',
      title: t('settings.danger.title'),
      items: [
        {
          id: 'reset_settings',
          title: t('settings.danger.resetSettings'),
          description: t('settings.danger.resetSettingsDesc'),
          type: 'action',
          icon: 'refresh-cw',
          danger: true,
          onPress: handleResetSettings,
        },
        {
          id: 'clear_data',
          title: t('settings.danger.clearData'),
          description: t('settings.danger.clearDataDesc'),
          type: 'action',
          icon: 'trash-2',
          danger: true,
          onPress: handleClearData,
        },
        {
          id: 'delete_account',
          title: t('settings.danger.deleteAccount'),
          description: t('settings.danger.deleteAccountDesc'),
          type: 'action',
          icon: 'user-x',
          danger: true,
          onPress: handleDeleteAccount,
        },
      ],
    },
  ];

  // Handlers
  function handleResetSettings() {
    Alert.alert(
      t('settings.danger.resetSettings'),
      t('settings.danger.resetConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.reset'),
          style: 'destructive',
          onPress: async () => {
            try {
              await resetSettings();
              Alert.alert(t('common.success'), t('settings.danger.resetSuccess'));
            } catch (error) {
              Alert.alert(t('common.error'), t('settings.danger.resetError'));
            }
          },
        },
      ]
    );
  }

  function handleClearData() {
    Alert.alert(
      t('settings.danger.clearData'),
      t('settings.danger.clearDataConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.clear'),
          style: 'destructive',
          onPress: () => {
            // Implement data clearing
            Alert.alert(t('common.success'), t('settings.danger.clearDataSuccess'));
          },
        },
      ]
    );
  }

  function handleDeleteAccount() {
    Alert.alert(
      t('settings.danger.deleteAccount'),
      t('settings.danger.deleteAccountConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => {
            // Implement account deletion
            navigation.navigate('DeleteAccount');
          },
        },
      ]
    );
  }

  const handleExportSettings = async () => {
    try {
      const exported = await exportSettings();
      // Handle export (share, save to file, etc.)
      Alert.alert(t('common.success'), t('settings.export.success'));
    } catch (error) {
      Alert.alert(t('common.error'), t('settings.export.error'));
    }
  };

  const renderSettingItem = (item: SettingItem) => (
    <SettingsItem
      key={item.id}
      title={item.title}
      description={item.description}
      icon={item.icon}
      danger={item.danger}
      rightElement={
        item.type === 'toggle' ? (
          <Switch
            value={item.value as boolean}
            onValueChange={item.onToggle}
            trackColor={{
              false: theme.colors.disabled,
              true: theme.colors.primary.main,
            }}
            thumbColor={theme.colors.surface}
          />
        ) : item.type === 'info' ? (
          <Text variant="body2" color="textSecondary">
            {item.value as string}
          </Text>
        ) : undefined
      }
      onPress={item.onPress}
    />
  );

  return (
    <Container padding="none">
      {/* Header */}
      <View style={styles.header}>
        <Text variant="subtitle1" weight="bold" i18nKey="settings.title" />
        <Button
          title=""
          icon="download"
          variant="ghost"
          size="sm"
          onPress={handleExportSettings}
        />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Privacy Score */}
        <View style={styles.section}>
          <PrivacyScoreCard
            score={privacyScore}
            recommendations={[
              t('privacy.recommendations.encryption'),
              t('privacy.recommendations.localAI'),
            ]}
          />
        </View>

        {/* Data Usage */}
        <View style={styles.section}>
          <DataUsageCard
            dataUsage={dataUsage}
            onViewDetails={() => navigation.navigate('DataUsageDetails')}
          />
        </View>

        {/* Security Status */}
        <View style={styles.section}>
          <SecurityStatusCard
            status={securityStatus}
            onViewDetails={() => navigation.navigate('SecurityDetails')}
          />
        </View>

        {/* Settings Sections */}
        {settingsGroups.map((group) => (
          <View key={group.id} style={styles.section}>
            <SettingsSection title={group.title}>
              {group.items.map(renderSettingItem)}
            </SettingsSection>
          </View>
        ))}

        {/* App Info */}
        <View style={styles.section}>
          <Card>
            <Text
              variant="caption"
              color="textSecondary"
              align="center"
              style={styles.appInfo}
            >
              {t('settings.appInfo', { 
                version: '1.0.0',
                build: '100'
              })}
            </Text>
          </Card>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  appInfo: {
    marginTop: 8,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default PrivacySettingsScreen;
