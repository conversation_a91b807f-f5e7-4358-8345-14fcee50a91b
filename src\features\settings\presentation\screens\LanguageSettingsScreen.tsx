/**
 * Language Settings Screen
 * 
 * Interface for selecting and changing app language with live preview
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  I18nManager,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { 
  SUPPORTED_LANGUAGES, 
  SupportedLanguage, 
  changeLanguage, 
  getCurrentLanguageInfo,
  isRTL 
} from '@/locales/i18n';

interface LanguageOption {
  code: SupportedLanguage;
  name: string;
  nameEn: string;
  flag: string;
  rtl: boolean;
  selected: boolean;
}

const LanguageSettingsScreen: React.FC = () => {
  const { t, i18n } = useTranslation(['settings', 'common']);
  const navigation = useNavigation();

  // State
  const [loading, setLoading] = useState(false);
  const [changingLanguage, setChangingLanguage] = useState<SupportedLanguage | null>(null);
  const [languages, setLanguages] = useState<LanguageOption[]>([]);

  // Load languages on mount
  useEffect(() => {
    loadLanguages();
  }, [i18n.language]);

  const loadLanguages = () => {
    const currentLanguage = i18n.language as SupportedLanguage;
    
    const languageOptions: LanguageOption[] = SUPPORTED_LANGUAGES.map(lang => ({
      ...lang,
      selected: lang.code === currentLanguage,
    }));

    setLanguages(languageOptions);
  };

  const handleLanguageChange = async (languageCode: SupportedLanguage) => {
    if (languageCode === i18n.language) {
      return; // Already selected
    }

    try {
      setChangingLanguage(languageCode);
      setLoading(true);

      // Show confirmation for RTL languages
      const targetLanguage = SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
      if (targetLanguage?.rtl !== isRTL()) {
        const shouldChange = await showRTLConfirmation(targetLanguage);
        if (!shouldChange) {
          setChangingLanguage(null);
          setLoading(false);
          return;
        }
      }

      // Change language
      await changeLanguage(languageCode);

      // Update local state
      loadLanguages();

      // Show success message
      Alert.alert(
        t('common:success'),
        t('settings:messages.saved'),
        [{ text: t('common:actions.ok') }]
      );

      // Navigate back after a short delay to show the change
      setTimeout(() => {
        navigation.goBack();
      }, 1000);

    } catch (error) {
      console.error('Failed to change language:', error);
      Alert.alert(
        t('common:error'),
        t('settings:messages.featureUnavailable'),
        [{ text: t('common:actions.ok') }]
      );
    } finally {
      setChangingLanguage(null);
      setLoading(false);
    }
  };

  const showRTLConfirmation = (targetLanguage: typeof SUPPORTED_LANGUAGES[number]): Promise<boolean> => {
    return new Promise((resolve) => {
      const isTargetRTL = targetLanguage.rtl;
      const message = isTargetRTL
        ? 'Switching to Arabic will change the app layout to right-to-left. Continue?'
        : 'تغيير اللغة إلى الإنجليزية سيغير تخطيط التطبيق من اليمين إلى اليسار. المتابعة؟';

      Alert.alert(
        'Language Change',
        message,
        [
          {
            text: isTargetRTL ? 'Cancel' : 'إلغاء',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: isTargetRTL ? 'Continue' : 'متابعة',
            onPress: () => resolve(true),
          },
        ]
      );
    });
  };

  const renderLanguageOption = (language: LanguageOption) => (
    <TouchableOpacity
      key={language.code}
      style={[
        styles.languageOption,
        language.selected && styles.selectedLanguageOption,
        changingLanguage === language.code && styles.changingLanguageOption,
      ]}
      onPress={() => handleLanguageChange(language.code)}
      disabled={loading}
    >
      <View style={styles.languageInfo}>
        <Text style={styles.languageFlag}>{language.flag}</Text>
        <View style={styles.languageText}>
          <Text style={[
            styles.languageName,
            language.selected && styles.selectedLanguageName,
          ]}>
            {language.name}
          </Text>
          <Text style={[
            styles.languageNameEn,
            language.selected && styles.selectedLanguageNameEn,
          ]}>
            {language.nameEn}
          </Text>
          {language.rtl && (
            <Text style={styles.rtlIndicator}>
              {t('settings:general.language.rtl', { defaultValue: 'Right-to-Left' })}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.languageActions}>
        {changingLanguage === language.code ? (
          <ActivityIndicator size="small" color="#2196F3" />
        ) : language.selected ? (
          <Icon name="check-circle" size={24} color="#4CAF50" />
        ) : (
          <Icon name="radio-button-unchecked" size={24} color="#ccc" />
        )}
      </View>
    </TouchableOpacity>
  );

  const renderPreview = () => {
    const currentLangInfo = getCurrentLanguageInfo();
    
    return (
      <View style={styles.previewSection}>
        <Text style={styles.previewTitle}>
          {t('settings:general.language.preview', { defaultValue: 'Preview' })}
        </Text>
        <View style={styles.previewCard}>
          <Text style={styles.previewText}>
            {t('common:app.name')}
          </Text>
          <Text style={styles.previewSubtext}>
            {t('common:app.tagline')}
          </Text>
          <View style={styles.previewActions}>
            <TouchableOpacity style={styles.previewButton}>
              <Text style={styles.previewButtonText}>
                {t('common:actions.save')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.previewButton, styles.previewButtonSecondary]}>
              <Text style={[styles.previewButtonText, styles.previewButtonTextSecondary]}>
                {t('common:actions.cancel')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <Text style={styles.previewNote}>
          {t('settings:general.language.previewNote', { 
            defaultValue: 'This is how the app will look in {{language}}',
            language: currentLangInfo.name 
          })}
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{t('settings:general.language.title')}</Text>
          <Text style={styles.subtitle}>
            {t('settings:general.language.description')}
          </Text>
        </View>

        {/* Current Language */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t('common:languages.current')}
          </Text>
          <Text style={styles.currentLanguage}>
            {getCurrentLanguageInfo().name} ({getCurrentLanguageInfo().nameEn})
          </Text>
        </View>

        {/* Language Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t('common:languages.select')}
          </Text>
          <View style={styles.languageList}>
            {languages.map(renderLanguageOption)}
          </View>
        </View>

        {/* Preview */}
        {renderPreview()}

        {/* Information */}
        <View style={styles.infoSection}>
          <View style={styles.infoItem}>
            <Icon name="info" size={20} color="#2196F3" />
            <Text style={styles.infoText}>
              {t('settings:general.language.info1', { 
                defaultValue: 'Language changes will be applied immediately' 
              })}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Icon name="restart-alt" size={20} color="#FF9800" />
            <Text style={styles.infoText}>
              {t('settings:general.language.info2', { 
                defaultValue: 'Some changes may require app restart' 
              })}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Icon name="storage" size={20} color="#4CAF50" />
            <Text style={styles.infoText}>
              {t('settings:general.language.info3', { 
                defaultValue: 'Your language preference is saved locally' 
              })}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Loading Overlay */}
      {loading && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingCard}>
            <ActivityIndicator size="large" color="#2196F3" />
            <Text style={styles.loadingText}>
              {t('settings:general.language.changing', { 
                defaultValue: 'Changing language...' 
              })}
            </Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  currentLanguage: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: '500',
  },
  languageList: {
    gap: 12,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#fafafa',
  },
  selectedLanguageOption: {
    borderColor: '#4CAF50',
    backgroundColor: '#f1f8e9',
  },
  changingLanguageOption: {
    borderColor: '#2196F3',
    backgroundColor: '#e3f2fd',
  },
  languageInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 32,
    marginRight: 16,
  },
  languageText: {
    flex: 1,
  },
  languageName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  selectedLanguageName: {
    color: '#4CAF50',
  },
  languageNameEn: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  selectedLanguageNameEn: {
    color: '#4CAF50',
  },
  rtlIndicator: {
    fontSize: 12,
    color: '#FF9800',
    fontStyle: 'italic',
  },
  languageActions: {
    marginLeft: 16,
  },
  previewSection: {
    backgroundColor: '#fff',
    marginTop: 16,
    padding: 20,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  previewCard: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    marginBottom: 12,
  },
  previewText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  previewSubtext: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  previewActions: {
    flexDirection: 'row',
    gap: 12,
  },
  previewButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#2196F3',
  },
  previewButtonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  previewButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  previewButtonTextSecondary: {
    color: '#2196F3',
  },
  previewNote: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: '#fff',
    marginTop: 16,
    marginBottom: 20,
    padding: 20,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
    lineHeight: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingCard: {
    backgroundColor: '#fff',
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 200,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
});

export default LanguageSettingsScreen;
