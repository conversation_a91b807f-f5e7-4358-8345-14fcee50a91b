/**
 * LifeAI Assistant - Main App Component
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  ScrollView,
  SafeAreaView,
} from 'react-native';

const App: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        <Text style={styles.title}>🤖 LifeAI Assistant</Text>
        <Text style={styles.subtitle}>مساعد الحياة الذكي</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>✅ App is running successfully!</Text>
          <Text style={styles.sectionTitle}>✅ التطبيق يعمل بنجاح!</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.description}>
            AI-powered mobile app for life management
          </Text>
          <Text style={styles.description}>
            تطبيق ذكي مدعوم بالذكاء الاصطناعي لإدارة الحياة
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.featureTitle}>Features / الميزات:</Text>
          <Text style={styles.feature}>🧠 Smart Planner / المخطط الذكي</Text>
          <Text style={styles.feature}>❤️ Health AI / الذكاء الاصطناعي الصحي</Text>
          <Text style={styles.feature}>📚 Learning AI / ذكاء التعلم</Text>
          <Text style={styles.feature}>🛒 Smart Shopping / التسوق الذكي</Text>
          <Text style={styles.feature}>💬 Chat AI / الذكاء الاصطناعي للمحادثة</Text>
          <Text style={styles.feature}>🔒 Privacy & Local AI / الخصوصية والذكاء الاصطناعي المحلي</Text>
          <Text style={styles.feature}>📱 Camera/AR / الكاميرا/الواقع المعزز</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.info}>Platform: Android</Text>
          <Text style={styles.info}>Environment: Development</Text>
          <Text style={styles.info}>Hot Reload: Enabled</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#4CAF50',
    marginBottom: 30,
    textAlign: 'center',
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    marginVertical: 10,
    borderRadius: 10,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    textAlign: 'left',
  },
  info: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
});

export default App;
