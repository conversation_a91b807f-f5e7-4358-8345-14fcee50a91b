# Environment Variables Template for LifeAI Assistant
# Copy this file to .env and fill in your actual values

# =============================================================================
# EXPO CONFIGURATION
# =============================================================================
EXPO_TOKEN=your_expo_token_here
EXPO_PROJECT_ID=your_eas_project_id

# =============================================================================
# iOS CONFIGURATION
# =============================================================================
# Apple Developer Account
EXPO_APPLE_ID=<EMAIL>
EXPO_ASC_APP_ID=your_app_store_connect_app_id
EXPO_APPLE_TEAM_ID=your_apple_team_id

# Fastlane iOS
FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD=your_app_specific_password
FASTLANE_SESSION=your_fastlane_session_token
MATCH_PASSWORD=your_match_encryption_password
MATCH_GIT_URL=https://github.com/your-org/certificates.git

# =============================================================================
# ANDROID CONFIGURATION
# =============================================================================
# Keystore Configuration
ANDROID_KEYSTORE_PASSWORD=your_keystore_password
ANDROID_KEY_ALIAS=lifeai-release-key
ANDROID_KEY_PASSWORD=your_key_password
ANDROID_KEYSTORE_BASE64=base64_encoded_keystore_content

# Google Play Console
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON=service_account_json_content
EXPO_GOOGLE_SERVICE_ACCOUNT_KEY_PATH=./android/service-account-key.json

# =============================================================================
# API KEYS AND SERVICES
# =============================================================================
# OpenAI API
OPENAI_API_KEY=your_openai_api_key

# Google Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key

# Firebase
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_MESSAGING_SENDER_ID=your_firebase_sender_id
FIREBASE_APP_ID=your_firebase_app_id

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Analytics
MIXPANEL_TOKEN=your_mixpanel_token
AMPLITUDE_API_KEY=your_amplitude_api_key

# Crash Reporting
SENTRY_DSN=your_sentry_dsn
BUGSNAG_API_KEY=your_bugsnag_api_key

# Push Notifications
ONESIGNAL_APP_ID=your_onesignal_app_id

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Flipper (Development only)
FLIPPER_ENABLED=true

# Metro Bundler
METRO_PORT=8081

# =============================================================================
# CI/CD CONFIGURATION
# =============================================================================
# Slack Notifications (Optional)
SLACK_URL=your_slack_webhook_url
SLACK_CHANNEL=#releases

# GitHub
GITHUB_TOKEN=your_github_token

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Encryption Keys (Generate secure random strings)
APP_SECRET_KEY=your_app_secret_key_32_chars_min
ENCRYPTION_KEY=your_encryption_key_32_chars_min
JWT_SECRET=your_jwt_secret_key

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/Disable Features
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_LOCAL_AI=true
ENABLE_SAFE_MODE=true

# =============================================================================
# ENVIRONMENT SPECIFIC
# =============================================================================
NODE_ENV=development
APP_ENV=development
DEBUG=true

# API Base URLs
API_BASE_URL=https://api.lifeai.app
API_VERSION=v1

# =============================================================================
# LOCALIZATION
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,ar
ENABLE_RTL=true

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# Local Storage
MAX_STORAGE_SIZE=100MB
ENABLE_ENCRYPTION=true
BACKUP_ENABLED=true

# =============================================================================
# AI CONFIGURATION
# =============================================================================
# AI Models
DEFAULT_AI_MODEL=gpt-3.5-turbo
ENABLE_LOCAL_AI=true
AI_RESPONSE_TIMEOUT=30000

# Privacy Settings
ENABLE_DATA_COLLECTION=false
ENABLE_TELEMETRY=false
SAFE_MODE_DEFAULT=true

# =============================================================================
# PERFORMANCE MONITORING
# =============================================================================
# Performance Thresholds
MAX_BUNDLE_SIZE=50MB
TARGET_FPS=60
MEMORY_WARNING_THRESHOLD=80

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
# Test Environment
TEST_API_BASE_URL=https://test-api.lifeai.app
ENABLE_TEST_MODE=false
MOCK_API_RESPONSES=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Build Configuration
ENABLE_PROGUARD=true
ENABLE_HERMES=true
ENABLE_FLIPPER_RELEASE=false

# Store Configuration
APP_STORE_TEAM_ID=your_app_store_team_id
GOOGLE_PLAY_TRACK=production

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# Log Levels
LOG_LEVEL=info
ENABLE_CONSOLE_LOGS=true
ENABLE_FILE_LOGS=false

# Monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_NETWORK_MONITORING=true
ENABLE_ERROR_BOUNDARY=true

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit this file with real values to version control
# 2. Use different values for development, staging, and production
# 3. Rotate sensitive keys regularly
# 4. Use secure random generators for encryption keys
# 5. Keep backups of important keys in secure storage
# 6. Use environment-specific .env files (.env.development, .env.production)
# 7. Validate all environment variables on app startup
# 8. Use tools like dotenv-safe for validation
