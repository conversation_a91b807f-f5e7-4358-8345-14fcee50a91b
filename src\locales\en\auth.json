{"welcome": {"title": "Welcome to LifeAI", "subtitle": "Your intelligent life companion", "description": "Experience the power of AI to enhance your daily life with personalized assistance, smart planning, and secure privacy controls."}, "login": {"title": "Sign In", "subtitle": "Welcome back! Sign in to continue", "email": "Email Address", "password": "Password", "forgotPassword": "Forgot Password?", "rememberMe": "Remember me", "signIn": "Sign In", "signInWith": "Sign in with", "noAccount": "Don't have an account?", "createAccount": "Create Account", "biometric": {"title": "Biometric Sign In", "subtitle": "Use your fingerprint or face to sign in", "prompt": "Authenti<PERSON> to sign in", "fallback": "Use password instead", "enable": "Enable biometric sign in", "disable": "Disable biometric sign in"}}, "register": {"title": "Create Account", "subtitle": "Join LifeAI and start your journey", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "phone": "Phone Number (Optional)", "agreeTerms": "I agree to the Terms of Service and Privacy Policy", "createAccount": "Create Account", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign In"}, "forgotPassword": {"title": "Reset Password", "subtitle": "Enter your email to receive reset instructions", "email": "Email Address", "sendInstructions": "Send Instructions", "backToLogin": "Back to Sign In", "checkEmail": "Check your email for reset instructions", "resendInstructions": "Resend Instructions"}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter your new password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "resetPassword": "Reset Password", "passwordReset": "Password reset successfully"}, "verification": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "We've sent a verification code to your email", "code": "Verification Code", "verify": "Verify", "resendCode": "Resend Code", "changeEmail": "Change Email", "verified": "Email verified successfully"}, "onboarding": {"welcome": {"title": "Welcome to LifeAI", "description": "Your personal AI assistant for a smarter life"}, "privacy": {"title": "Privacy First", "description": "Your data stays secure with local encryption and privacy controls"}, "features": {"title": "Powerful Features", "description": "Smart planning, health AI, learning assistance, and more"}, "safeMode": {"title": "Safe Mode", "description": "Work offline with local AI when privacy is paramount"}, "getStarted": "Get Started", "skip": "<PERSON><PERSON>", "next": "Next", "previous": "Previous", "finish": "Finish Setup"}, "logout": {"title": "Sign Out", "message": "Are you sure you want to sign out?", "confirm": "Sign Out", "cancel": "Cancel"}, "deleteAccount": {"title": "Delete Account", "message": "This action cannot be undone. All your data will be permanently deleted.", "confirm": "Delete Account", "cancel": "Cancel", "password": "Enter your password to confirm", "deleted": "Account deleted successfully"}, "errors": {"invalidCredentials": "Invalid email or password", "emailExists": "An account with this email already exists", "emailNotFound": "No account found with this email", "weakPassword": "Password is too weak", "networkError": "Network error. Please check your connection", "serverError": "Server error. Please try again later", "invalidCode": "Invalid verification code", "codeExpired": "Verification code has expired", "biometricNotAvailable": "Biometric authentication is not available", "biometricFailed": "Biometric authentication failed", "accountDisabled": "Your account has been disabled", "tooManyAttempts": "Too many failed attempts. Please try again later"}, "success": {"accountCreated": "Account created successfully", "passwordReset": "Password reset successfully", "emailVerified": "Email verified successfully", "profileUpdated": "Profile updated successfully", "settingsSaved": "Setting<PERSON> saved successfully"}, "social": {"google": "Google", "apple": "Apple", "facebook": "Facebook", "twitter": "Twitter", "signInWith": "Sign in with {{provider}}", "continueWith": "Continue with {{provider}}"}, "terms": {"title": "Terms of Service", "privacy": "Privacy Policy", "accept": "Accept", "decline": "Decline", "readAndAccept": "I have read and accept the {{document}}"}}