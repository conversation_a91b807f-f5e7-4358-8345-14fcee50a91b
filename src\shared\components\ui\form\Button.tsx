/**
 * Button Component
 * 
 * زر أساسي مع أنماط متعددة وحالات مختلفة
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  TouchableOpacityProps,
} from 'react-native';
import { useTheme } from '@/ui/theme/useTheme';
import { DESIGN_TOKENS } from '../index';
import Icon from '../display/Icon';

export interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  onPress,
  ...props
}) => {
  const theme = useTheme();

  const sizeConfig = {
    sm: {
      paddingVertical: DESIGN_TOKENS.spacing.sm,
      paddingHorizontal: DESIGN_TOKENS.spacing.md,
      fontSize: DESIGN_TOKENS.fontSize.sm,
      iconSize: 16,
    },
    md: {
      paddingVertical: DESIGN_TOKENS.spacing.md,
      paddingHorizontal: DESIGN_TOKENS.spacing.lg,
      fontSize: DESIGN_TOKENS.fontSize.md,
      iconSize: 20,
    },
    lg: {
      paddingVertical: DESIGN_TOKENS.spacing.lg,
      paddingHorizontal: DESIGN_TOKENS.spacing.xl,
      fontSize: DESIGN_TOKENS.fontSize.lg,
      iconSize: 24,
    },
  };

  const variantConfig = {
    primary: {
      backgroundColor: theme.colors.primary.main,
      textColor: theme.colors.primary.contrastText,
      borderColor: 'transparent',
    },
    secondary: {
      backgroundColor: theme.colors.secondary.main,
      textColor: theme.colors.secondary.contrastText,
      borderColor: 'transparent',
    },
    outline: {
      backgroundColor: 'transparent',
      textColor: theme.colors.primary.main,
      borderColor: theme.colors.primary.main,
    },
    ghost: {
      backgroundColor: 'transparent',
      textColor: theme.colors.text,
      borderColor: 'transparent',
    },
    danger: {
      backgroundColor: theme.colors.error.main,
      textColor: theme.colors.error.contrastText,
      borderColor: 'transparent',
    },
  };

  const currentSize = sizeConfig[size];
  const currentVariant = variantConfig[variant];

  const buttonStyle: ViewStyle = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: currentSize.paddingVertical,
    paddingHorizontal: currentSize.paddingHorizontal,
    backgroundColor: disabled 
      ? theme.colors.disabled 
      : currentVariant.backgroundColor,
    borderRadius: DESIGN_TOKENS.radius.md,
    borderWidth: variant === 'outline' ? 1 : 0,
    borderColor: disabled 
      ? theme.colors.disabled 
      : currentVariant.borderColor,
    width: fullWidth ? '100%' : undefined,
    opacity: disabled ? 0.6 : 1,
  };

  const buttonTextStyle: TextStyle = {
    fontSize: currentSize.fontSize,
    fontWeight: DESIGN_TOKENS.fontWeight.semibold,
    color: disabled 
      ? theme.colors.textSecondary 
      : currentVariant.textColor,
    marginLeft: icon && iconPosition === 'left' ? DESIGN_TOKENS.spacing.sm : 0,
    marginRight: icon && iconPosition === 'right' ? DESIGN_TOKENS.spacing.sm : 0,
  };

  const handlePress = () => {
    if (!disabled && !loading && onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      style={[buttonStyle, style]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          size="small" 
          color={currentVariant.textColor} 
        />
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <Icon 
              name={icon} 
              size={currentSize.iconSize} 
              color={currentVariant.textColor} 
            />
          )}
          <Text style={[buttonTextStyle, textStyle]}>
            {title}
          </Text>
          {icon && iconPosition === 'right' && (
            <Icon 
              name={icon} 
              size={currentSize.iconSize} 
              color={currentVariant.textColor} 
            />
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

export default Button;
