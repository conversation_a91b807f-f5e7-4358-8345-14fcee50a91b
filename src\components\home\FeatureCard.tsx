/**
 * Feature Card Component
 * 
 * بطاقة ميزة في الشاشة الرئيسية
 */

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Card, Text } from '@/shared/components/ui';
import Icon from '@/shared/components/ui/display/Icon';
import Badge from '@/shared/components/ui/display/Badge';
import { useTheme } from '@/ui/theme/useTheme';

interface FeatureItem {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
  route: string;
  badge?: string;
}

interface FeatureCardProps {
  feature: FeatureItem;
  onPress: () => void;
  style?: ViewStyle;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  feature,
  onPress,
  style,
}) => {
  const theme = useTheme();

  return (
    <Card
      style={[styles.container, style]}
      onPress={onPress}
      pressable
      padding="lg"
    >
      {/* Badge */}
      {feature.badge && (
        <Badge
          text={feature.badge}
          variant="primary"
          size="sm"
          style={styles.badge}
        />
      )}

      {/* Icon */}
      <View style={[styles.iconContainer, { backgroundColor: `${feature.color}15` }]}>
        <Icon
          name={feature.icon}
          size={28}
          color={feature.color}
        />
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text
          variant="subtitle2"
          weight="semibold"
          numberOfLines={1}
          style={styles.title}
        >
          {feature.title}
        </Text>
        <Text
          variant="caption"
          color="textSecondary"
          numberOfLines={2}
          style={styles.subtitle}
        >
          {feature.subtitle}
        </Text>
      </View>

      {/* Arrow Icon */}
      <View style={styles.arrowContainer}>
        <Icon
          name="chevron-right"
          size={16}
          color={theme.colors.textSecondary}
        />
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    minHeight: 120,
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  content: {
    flex: 1,
  },
  title: {
    marginBottom: 4,
  },
  subtitle: {
    lineHeight: 16,
  },
  arrowContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
  },
});

export default FeatureCard;
