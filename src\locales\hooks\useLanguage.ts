/**
 * Language Hook
 * 
 * React hook for managing language state and changes
 */

import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { I18nManager } from 'react-native';
import { 
  SUPPORTED_LANGUAGES, 
  SupportedLanguage, 
  changeLanguage, 
  getCurrentLanguageInfo,
  isRTL,
  addLanguageChangeListener,
  removeLanguageChangeListener,
  getTextDirection,
  getTextAlign,
  getFlexDirection 
} from '../i18n';

interface UseLanguageReturn {
  // Current language info
  currentLanguage: SupportedLanguage;
  currentLanguageInfo: typeof SUPPORTED_LANGUAGES[number];
  isCurrentRTL: boolean;
  
  // Available languages
  supportedLanguages: typeof SUPPORTED_LANGUAGES;
  
  // Language change
  changeLanguage: (language: SupportedLanguage) => Promise<void>;
  isChangingLanguage: boolean;
  
  // RTL helpers
  textDirection: 'ltr' | 'rtl';
  textAlign: 'left' | 'right';
  flexDirection: 'row' | 'row-reverse';
  
  // Language detection
  isLanguageSupported: (languageCode: string) => boolean;
  getLanguageInfo: (languageCode: SupportedLanguage) => typeof SUPPORTED_LANGUAGES[number] | undefined;
  
  // Events
  onLanguageChange: (callback: (language: SupportedLanguage) => void) => () => void;
}

export const useLanguage = (): UseLanguageReturn => {
  const { i18n } = useTranslation();
  
  // State
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    i18n.language as SupportedLanguage
  );

  // Update current language when i18n language changes
  useEffect(() => {
    const handleLanguageChange = (language: SupportedLanguage) => {
      setCurrentLanguage(language);
    };

    // Listen to language changes
    addLanguageChangeListener(handleLanguageChange);

    // Update current language
    setCurrentLanguage(i18n.language as SupportedLanguage);

    return () => {
      removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18n.language]);

  // Change language function
  const handleChangeLanguage = useCallback(async (language: SupportedLanguage) => {
    if (language === currentLanguage) {
      return;
    }

    try {
      setIsChangingLanguage(true);
      await changeLanguage(language);
    } catch (error) {
      console.error('Failed to change language:', error);
      throw error;
    } finally {
      setIsChangingLanguage(false);
    }
  }, [currentLanguage]);

  // Check if language is supported
  const isLanguageSupported = useCallback((languageCode: string): boolean => {
    return SUPPORTED_LANGUAGES.some(lang => lang.code === languageCode);
  }, []);

  // Get language info
  const getLanguageInfo = useCallback((languageCode: SupportedLanguage) => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === languageCode);
  }, []);

  // Add language change listener
  const onLanguageChange = useCallback((callback: (language: SupportedLanguage) => void) => {
    addLanguageChangeListener(callback);
    
    // Return cleanup function
    return () => {
      removeLanguageChangeListener(callback);
    };
  }, []);

  // Computed values
  const currentLanguageInfo = getCurrentLanguageInfo();
  const isCurrentRTL = isRTL();
  const textDirection = getTextDirection();
  const textAlign = getTextAlign();
  const flexDirection = getFlexDirection();

  return {
    // Current language info
    currentLanguage,
    currentLanguageInfo,
    isCurrentRTL,
    
    // Available languages
    supportedLanguages: SUPPORTED_LANGUAGES,
    
    // Language change
    changeLanguage: handleChangeLanguage,
    isChangingLanguage,
    
    // RTL helpers
    textDirection,
    textAlign,
    flexDirection,
    
    // Language detection
    isLanguageSupported,
    getLanguageInfo,
    
    // Events
    onLanguageChange,
  };
};

/**
 * Hook for RTL-aware styles
 */
export const useRTLStyles = () => {
  const { isCurrentRTL, textDirection, textAlign, flexDirection } = useLanguage();

  const getRTLStyle = useCallback((ltrStyle: any, rtlStyle: any) => {
    return isCurrentRTL ? rtlStyle : ltrStyle;
  }, [isCurrentRTL]);

  const getDirectionalStyle = useCallback((property: string, ltrValue: any, rtlValue: any) => {
    return {
      [property]: isCurrentRTL ? rtlValue : ltrValue,
    };
  }, [isCurrentRTL]);

  const getMarginStyle = useCallback((left: number, right: number) => {
    return isCurrentRTL 
      ? { marginLeft: right, marginRight: left }
      : { marginLeft: left, marginRight: right };
  }, [isCurrentRTL]);

  const getPaddingStyle = useCallback((left: number, right: number) => {
    return isCurrentRTL 
      ? { paddingLeft: right, paddingRight: left }
      : { paddingLeft: left, paddingRight: right };
  }, [isCurrentRTL]);

  const getPositionStyle = useCallback((left?: number, right?: number) => {
    if (left !== undefined && right !== undefined) {
      return isCurrentRTL 
        ? { left: right, right: left }
        : { left, right };
    }
    if (left !== undefined) {
      return isCurrentRTL ? { right: left } : { left };
    }
    if (right !== undefined) {
      return isCurrentRTL ? { left: right } : { right };
    }
    return {};
  }, [isCurrentRTL]);

  const getBorderStyle = useCallback((leftWidth?: number, rightWidth?: number, leftColor?: string, rightColor?: string) => {
    const style: any = {};
    
    if (leftWidth !== undefined && rightWidth !== undefined) {
      if (isCurrentRTL) {
        style.borderLeftWidth = rightWidth;
        style.borderRightWidth = leftWidth;
      } else {
        style.borderLeftWidth = leftWidth;
        style.borderRightWidth = rightWidth;
      }
    }
    
    if (leftColor && rightColor) {
      if (isCurrentRTL) {
        style.borderLeftColor = rightColor;
        style.borderRightColor = leftColor;
      } else {
        style.borderLeftColor = leftColor;
        style.borderRightColor = rightColor;
      }
    }
    
    return style;
  }, [isCurrentRTL]);

  return {
    isRTL: isCurrentRTL,
    textDirection,
    textAlign,
    flexDirection,
    getRTLStyle,
    getDirectionalStyle,
    getMarginStyle,
    getPaddingStyle,
    getPositionStyle,
    getBorderStyle,
  };
};

/**
 * Hook for language-aware formatting
 */
export const useLanguageFormatting = () => {
  const { currentLanguage } = useLanguage();

  const formatNumber = useCallback((number: number): string => {
    const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    try {
      return new Intl.NumberFormat(locale).format(number);
    } catch (error) {
      return number.toString();
    }
  }, [currentLanguage]);

  const formatDate = useCallback((date: Date, options?: Intl.DateTimeFormatOptions): string => {
    const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    
    try {
      return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options }).format(date);
    } catch (error) {
      return date.toLocaleDateString();
    }
  }, [currentLanguage]);

  const formatCurrency = useCallback((amount: number, currency: string = 'USD'): string => {
    const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
      }).format(amount);
    } catch (error) {
      return `${amount} ${currency}`;
    }
  }, [currentLanguage]);

  const formatRelativeTime = useCallback((date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    const locale = currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
    
    try {
      const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
      
      if (diffInSeconds < 60) {
        return rtf.format(-diffInSeconds, 'second');
      } else if (diffInSeconds < 3600) {
        return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
      } else if (diffInSeconds < 86400) {
        return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
      } else {
        return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
      }
    } catch (error) {
      return date.toLocaleDateString();
    }
  }, [currentLanguage]);

  return {
    formatNumber,
    formatDate,
    formatCurrency,
    formatRelativeTime,
  };
};

export default useLanguage;
