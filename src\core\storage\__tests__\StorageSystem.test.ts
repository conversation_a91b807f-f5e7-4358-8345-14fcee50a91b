/**
 * Storage System Tests
 * 
 * Comprehensive tests for the secure storage system
 */

import { storageManager, initializeStorage } from '../index';
import { EncryptionService } from '../EncryptionService';
import { SecureStorageService } from '../SecureStorageService';
import { PrivacyManager } from '../PrivacyManager';
import { SafeModeService } from '../SafeModeService';
import { DEFAULT_PRIVACY_SETTINGS } from '../types';

// Mock dependencies
jest.mock('react-native-mmkv');
jest.mock('react-native-keychain');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('@react-native-community/netinfo');

describe('Storage System', () => {
  beforeEach(async () => {
    // Reset storage system before each test
    await storageManager.reset();
  });

  describe('Initialization', () => {
    it('should initialize storage system successfully', async () => {
      const result = await initializeStorage();
      expect(storageManager.isInitialized()).toBe(true);
    });

    it('should handle initialization failure gracefully', async () => {
      // Mock a failure scenario
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // This should not throw
      await expect(initializeStorage()).resolves.not.toThrow();
    });
  });

  describe('Encryption Service', () => {
    let encryptionService: EncryptionService;

    beforeEach(async () => {
      encryptionService = new EncryptionService();
      await encryptionService.initialize();
    });

    it('should encrypt and decrypt data correctly', async () => {
      const originalData = 'Hello, World! مرحبا بالعالم';
      
      const encrypted = await encryptionService.encrypt(originalData);
      expect(encrypted.data).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.salt).toBeDefined();
      
      const decrypted = await encryptionService.decrypt(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should encrypt and decrypt objects correctly', async () => {
      const originalObject = {
        name: 'أحمد محمد',
        email: '<EMAIL>',
        preferences: {
          language: 'ar',
          theme: 'dark'
        }
      };
      
      const encrypted = await encryptionService.encryptObject(originalObject);
      const decrypted = await encryptionService.decryptObject(encrypted);
      
      expect(decrypted).toEqual(originalObject);
    });

    it('should generate secure random strings', () => {
      const random1 = encryptionService.generateSecureRandom(32);
      const random2 = encryptionService.generateSecureRandom(32);
      
      expect(random1).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(random2).toHaveLength(64);
      expect(random1).not.toBe(random2);
    });

    it('should verify data integrity with hashes', () => {
      const data = 'Test data for hashing';
      const hash = encryptionService.generateHash(data);
      
      expect(encryptionService.verifyHash(data, hash)).toBe(true);
      expect(encryptionService.verifyHash('Modified data', hash)).toBe(false);
    });
  });

  describe('Secure Storage Service', () => {
    let secureStorage: SecureStorageService;

    beforeEach(async () => {
      secureStorage = new SecureStorageService();
      await secureStorage.initialize();
    });

    it('should store and retrieve encrypted data', async () => {
      const testData = {
        message: 'مرحبا، هذا اختبار للتخزين الآمن',
        timestamp: Date.now()
      };
      
      await secureStorage.setItem('test_key', testData, { encrypt: true });
      const retrieved = await secureStorage.getItem('test_key');
      
      expect(retrieved).toEqual(testData);
    });

    it('should store and retrieve unencrypted data', async () => {
      const testData = { setting: 'value', number: 42 };
      
      await secureStorage.setItem('test_key', testData, { encrypt: false });
      const retrieved = await secureStorage.getItem('test_key');
      
      expect(retrieved).toEqual(testData);
    });

    it('should handle TTL expiration', async () => {
      const testData = { message: 'This will expire' };
      
      await secureStorage.setItem('test_key', testData, { 
        encrypt: true, 
        ttl: 100 // 100ms
      });
      
      // Should exist immediately
      let retrieved = await secureStorage.getItem('test_key');
      expect(retrieved).toEqual(testData);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should be null after expiration
      retrieved = await secureStorage.getItem('test_key');
      expect(retrieved).toBeNull();
    });

    it('should remove items correctly', async () => {
      await secureStorage.setItem('test_key', { data: 'test' });
      
      expect(await secureStorage.hasItem('test_key')).toBe(true);
      
      await secureStorage.removeItem('test_key');
      
      expect(await secureStorage.hasItem('test_key')).toBe(false);
      expect(await secureStorage.getItem('test_key')).toBeNull();
    });
  });

  describe('Privacy Manager', () => {
    let privacyManager: PrivacyManager;

    beforeEach(async () => {
      privacyManager = new PrivacyManager();
      await privacyManager.initialize();
    });

    it('should initialize with default privacy settings', () => {
      const settings = privacyManager.getPrivacySettings();
      expect(settings).toEqual(DEFAULT_PRIVACY_SETTINGS);
    });

    it('should update privacy settings correctly', async () => {
      await privacyManager.updatePrivacySettings({
        sendToExternalAI: true,
        shareWithThirdParties: true
      });
      
      const settings = privacyManager.getPrivacySettings();
      expect(settings.sendToExternalAI).toBe(true);
      expect(settings.shareWithThirdParties).toBe(true);
    });

    it('should calculate privacy score correctly', () => {
      const score = privacyManager.getPrivacyScore();
      expect(score).toBeGreaterThanOrEqual(0);
      expect(score).toBeLessThanOrEqual(100);
    });

    it('should control data sharing based on settings', () => {
      // Default settings should not allow external sharing
      expect(privacyManager.canShareData('chat_history', true)).toBe(false);
      expect(privacyManager.canShareData('personal_info')).toBe(false);
    });

    it('should enable and disable safe mode', async () => {
      expect(privacyManager.isSafeModeActive()).toBe(false);
      
      await privacyManager.enableSafeMode();
      expect(privacyManager.isSafeModeActive()).toBe(true);
      
      await privacyManager.disableSafeMode();
      expect(privacyManager.isSafeModeActive()).toBe(false);
    });

    it('should generate privacy audit report', async () => {
      // Log some privacy actions
      await privacyManager.logPrivacyAction('data_accessed', 'user_profile', false);
      await privacyManager.logPrivacyAction('data_shared', 'analytics', true);
      
      const report = await privacyManager.getPrivacyAuditReport(7);
      
      expect(report).toHaveProperty('totalActions');
      expect(report).toHaveProperty('dataSharedExternally');
      expect(report).toHaveProperty('dataKeptLocal');
    });
  });

  describe('Safe Mode Service', () => {
    let safeModeService: SafeModeService;

    beforeEach(async () => {
      safeModeService = new SafeModeService();
      await safeModeService.initialize();
    });

    it('should process text with local AI', async () => {
      const response = await safeModeService.processText('مرحبا', 'chat');
      
      expect(response).toHaveProperty('response');
      expect(response).toHaveProperty('confidence');
      expect(response).toHaveProperty('source');
      expect(response.source).toBe('local');
    });

    it('should detect language correctly', async () => {
      const arabicResponse = await safeModeService.processText('مرحبا كيف حالك؟', 'analyze');
      expect(arabicResponse.response).toContain('عربية');
      
      const englishResponse = await safeModeService.processText('Hello how are you?', 'analyze');
      expect(englishResponse.response).toContain('English');
    });

    it('should analyze sentiment', async () => {
      const positiveResponse = await safeModeService.processText('أنا سعيد جداً', 'analyze');
      expect(positiveResponse.response).toContain('positive');
      
      const negativeResponse = await safeModeService.processText('أنا حزين', 'analyze');
      expect(negativeResponse.response).toContain('negative');
    });

    it('should cache responses', async () => {
      const input = 'test message for caching';
      
      const response1 = await safeModeService.processText(input, 'chat');
      const response2 = await safeModeService.processText(input, 'chat');
      
      expect(response2.source).toBe('cached');
    });

    it('should add knowledge entries', async () => {
      await safeModeService.addKnowledgeEntry({
        category: 'test',
        question: 'test question',
        answer: 'test answer',
        keywords: ['test'],
        confidence: 0.9,
        language: 'en'
      });
      
      const stats = await safeModeService.getStatistics();
      expect(stats.knowledgeBaseEntries).toBeGreaterThan(0);
    });
  });

  describe('Storage Manager Integration', () => {
    beforeEach(async () => {
      await initializeStorage();
    });

    it('should provide unified interface for storage operations', async () => {
      const testData = { message: 'Integration test', value: 123 };
      
      await storageManager.store('integration_test', testData);
      const retrieved = await storageManager.retrieve('integration_test');
      
      expect(retrieved).toEqual(testData);
      
      await storageManager.remove('integration_test');
      const afterRemoval = await storageManager.retrieve('integration_test');
      
      expect(afterRemoval).toBeNull();
    });

    it('should export and import data correctly', async () => {
      // Store some test data
      await storageManager.store('test1', { data: 'value1' });
      await storageManager.store('test2', { data: 'value2' });
      
      // Export data
      const exportedData = await storageManager.exportData();
      expect(exportedData).toBeDefined();
      
      // Clear storage
      await storageManager.reset();
      
      // Import data
      await storageManager.importData(exportedData);
      
      // Verify data is restored
      const restored1 = await storageManager.retrieve('test1');
      const restored2 = await storageManager.retrieve('test2');
      
      expect(restored1).toEqual({ data: 'value1' });
      expect(restored2).toEqual({ data: 'value2' });
    });

    it('should get comprehensive statistics', async () => {
      const stats = await storageManager.getStatistics();
      
      expect(stats).toHaveProperty('database');
      expect(stats).toHaveProperty('safeMode');
      expect(stats).toHaveProperty('privacy');
      expect(stats).toHaveProperty('encryption');
    });

    it('should handle safe mode operations', async () => {
      await storageManager.enableSafeMode();
      
      const response = await storageManager.processWithLocalAI('Hello', 'chat');
      expect(response).toHaveProperty('response');
      
      await storageManager.disableSafeMode();
    });
  });

  describe('Error Handling', () => {
    it('should handle encryption errors gracefully', async () => {
      const encryptionService = new EncryptionService();
      
      // Try to encrypt without initialization
      await expect(encryptionService.encrypt('test')).rejects.toThrow();
    });

    it('should handle storage errors gracefully', async () => {
      const secureStorage = new SecureStorageService();
      
      // Try to store without initialization
      await expect(secureStorage.setItem('test', 'value')).rejects.toThrow();
    });

    it('should handle safe mode errors gracefully', async () => {
      const safeModeService = new SafeModeService();
      
      // Try to process without safe mode active
      await expect(safeModeService.processText('test', 'chat')).rejects.toThrow();
    });
  });
});

describe('Performance Tests', () => {
  beforeEach(async () => {
    await initializeStorage();
  });

  it('should encrypt/decrypt within performance limits', async () => {
    const encryptionService = storageManager.getEncryptionService();
    const testData = 'A'.repeat(1000); // 1KB of data
    
    const startTime = Date.now();
    const encrypted = await encryptionService.encrypt(testData);
    const decrypted = await encryptionService.decrypt(encrypted);
    const endTime = Date.now();
    
    expect(decrypted).toBe(testData);
    expect(endTime - startTime).toBeLessThan(100); // Should complete in < 100ms
  });

  it('should handle multiple concurrent operations', async () => {
    const operations = Array.from({ length: 10 }, (_, i) => 
      storageManager.store(`test_${i}`, { value: i })
    );
    
    await expect(Promise.all(operations)).resolves.not.toThrow();
    
    // Verify all data was stored
    for (let i = 0; i < 10; i++) {
      const retrieved = await storageManager.retrieve(`test_${i}`);
      expect(retrieved).toEqual({ value: i });
    }
  });
});
