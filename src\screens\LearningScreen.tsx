/**
 * Learning Screen - صفحة التعلم
 * 
 * شاشة التعلم الذاتي مع الذكاء الاصطناعي
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

// UI Components
import { Container, Card, Text, Button } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

// Feature Components
import LearningProgressCard from '../components/learning/LearningProgressCard';
import CourseCard from '../components/learning/CourseCard';
import SkillAssessmentCard from '../components/learning/SkillAssessmentCard';
import LearningPathCard from '../components/learning/LearningPathCard';
import StudySessionCard from '../components/learning/StudySessionCard';
import RecommendationsCard from '../components/learning/RecommendationsCard';
import AchievementsCard from '../components/learning/AchievementsCard';
import LearningGoalsCard from '../components/learning/LearningGoalsCard';

// Hooks
import { useLearningProgress } from '@/features/learning-ai/hooks/useLearningProgress';
import { askLearningAI } from '@/services/ai';

interface Course {
  id: string;
  title: string;
  description: string;
  progress: number;
  totalLessons: number;
  completedLessons: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
  estimatedTime: string;
  instructor: string;
  rating: number;
}

interface LearningGoal {
  id: string;
  title: string;
  description: string;
  progress: number;
  deadline: Date;
  skills: string[];
}

const LearningScreen: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigation = useNavigation();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'courses' | 'skills' | 'goals'>('overview');
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loadingRecommendations, setLoadingRecommendations] = useState(false);

  // Hooks
  const {
    currentCourses,
    completedCourses,
    learningGoals,
    skillProgress,
    studyStreak,
    totalStudyTime,
    achievements,
    isLoading,
    refreshProgress,
  } = useLearningProgress();

  // Effects
  useEffect(() => {
    loadRecommendations();
  }, [skillProgress, learningGoals]);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshProgress();
      await loadRecommendations();
    } catch (error) {
      console.error('Failed to refresh learning data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadRecommendations = async () => {
    if (!skillProgress || !learningGoals) return;

    setLoadingRecommendations(true);
    try {
      const response = await askLearningAI.recommendContent({
        skills: Object.keys(skillProgress),
        goals: learningGoals.map(g => g.title),
        currentLevel: 'intermediate',
        preferences: {
          learningStyle: 'visual',
          timeAvailable: '1-2 hours',
          difficulty: 'intermediate',
        },
      });
      setRecommendations(response.result.recommendations || []);
    } catch (error) {
      console.error('Failed to load recommendations:', error);
    } finally {
      setLoadingRecommendations(false);
    }
  };

  const handleCoursePress = (course: Course) => {
    navigation.navigate('CourseDetails', { courseId: course.id });
  };

  const handleStartStudySession = () => {
    navigation.navigate('StudySession');
  };

  const handleSkillAssessment = () => {
    navigation.navigate('SkillAssessment');
  };

  const handleViewLearningPath = () => {
    navigation.navigate('LearningPath');
  };

  const handleCreateGoal = () => {
    navigation.navigate('CreateLearningGoal');
  };

  const handleGoalPress = (goal: LearningGoal) => {
    navigation.navigate('LearningGoalDetails', { goalId: goal.id });
  };

  const handleBrowseCourses = () => {
    navigation.navigate('CourseCatalog');
  };

  const renderCourseItem = ({ item }: { item: Course }) => (
    <CourseCard
      course={item}
      onPress={() => handleCoursePress(item)}
      style={styles.courseCard}
    />
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <>
            {/* Learning Progress */}
            <View style={styles.section}>
              <LearningProgressCard
                studyStreak={studyStreak}
                totalStudyTime={totalStudyTime}
                completedCourses={completedCourses.length}
                skillsImproved={Object.keys(skillProgress).length}
              />
            </View>

            {/* Study Session */}
            <View style={styles.section}>
              <StudySessionCard
                onStartSession={handleStartStudySession}
                recommendedDuration={30}
                lastSessionDate={new Date()}
              />
            </View>

            {/* Current Courses */}
            <View style={styles.section}>
              <Text
                variant="subtitle2"
                weight="semibold"
                style={styles.sectionTitle}
                i18nKey="learning.currentCourses"
              />
              {currentCourses.length > 0 ? (
                <FlatList
                  data={currentCourses.slice(0, 3)}
                  renderItem={renderCourseItem}
                  keyExtractor={(item) => item.id}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.horizontalList}
                />
              ) : (
                <Card style={styles.emptyState}>
                  <Text
                    variant="body2"
                    color="textSecondary"
                    align="center"
                    i18nKey="learning.noCourses"
                  />
                  <Button
                    title={t('learning.browseCourses')}
                    variant="outline"
                    size="sm"
                    onPress={handleBrowseCourses}
                    style={styles.emptyStateButton}
                  />
                </Card>
              )}
            </View>

            {/* Recommendations */}
            <View style={styles.section}>
              <RecommendationsCard
                recommendations={recommendations}
                loading={loadingRecommendations}
                onViewMore={() => navigation.navigate('Recommendations')}
              />
            </View>
          </>
        );

      case 'courses':
        return (
          <View style={styles.section}>
            <Text
              variant="subtitle2"
              weight="semibold"
              style={styles.sectionTitle}
              i18nKey="learning.allCourses"
            />
            {currentCourses.length > 0 ? (
              <FlatList
                data={currentCourses}
                renderItem={renderCourseItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <Card style={styles.emptyState}>
                <Text
                  variant="body2"
                  color="textSecondary"
                  align="center"
                  i18nKey="learning.noCourses"
                />
                <Button
                  title={t('learning.browseCourses')}
                  variant="primary"
                  onPress={handleBrowseCourses}
                  style={styles.emptyStateButton}
                />
              </Card>
            )}
          </View>
        );

      case 'skills':
        return (
          <>
            <View style={styles.section}>
              <SkillAssessmentCard
                skillProgress={skillProgress}
                onTakeAssessment={handleSkillAssessment}
                onViewDetails={() => navigation.navigate('SkillDetails')}
              />
            </View>
            <View style={styles.section}>
              <LearningPathCard
                onViewPath={handleViewLearningPath}
                onCustomizePath={() => navigation.navigate('CustomizeLearningPath')}
              />
            </View>
          </>
        );

      case 'goals':
        return (
          <View style={styles.section}>
            <LearningGoalsCard
              goals={learningGoals}
              onGoalPress={handleGoalPress}
              onCreateGoal={handleCreateGoal}
            />
          </View>
        );

      default:
        return null;
    }
  };

  const tabs = [
    { id: 'overview', title: t('learning.tabs.overview'), icon: 'home' },
    { id: 'courses', title: t('learning.tabs.courses'), icon: 'book' },
    { id: 'skills', title: t('learning.tabs.skills'), icon: 'brain' },
    { id: 'goals', title: t('learning.tabs.goals'), icon: 'target' },
  ];

  return (
    <Container padding="none">
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text variant="subtitle1" weight="bold" i18nKey="learning.title" />
          <View style={styles.headerActions}>
            <Button
              title=""
              icon="search"
              variant="ghost"
              size="sm"
              onPress={() => navigation.navigate('SearchCourses')}
            />
            <Button
              title=""
              icon="plus"
              variant="primary"
              size="sm"
              onPress={handleCreateGoal}
            />
          </View>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabScrollView}
        >
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              title={tab.title}
              variant={activeTab === tab.id ? 'primary' : 'ghost'}
              size="sm"
              onPress={() => setActiveTab(tab.id as any)}
              style={styles.tabButton}
            />
          ))}
        </ScrollView>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary.main}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderTabContent()}

        {/* Achievements */}
        {activeTab === 'overview' && achievements.length > 0 && (
          <View style={styles.section}>
            <AchievementsCard
              achievements={achievements}
              onViewAll={() => navigation.navigate('Achievements')}
            />
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabScrollView: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    gap: 8,
  },
  tabButton: {
    minWidth: 80,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  courseCard: {
    marginBottom: 12,
  },
  horizontalList: {
    gap: 12,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateButton: {
    marginTop: 16,
    minWidth: 150,
  },
  bottomSpacing: {
    height: 100,
  },
});

export default LearningScreen;
