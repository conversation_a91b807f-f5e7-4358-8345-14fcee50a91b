/**
 * Storage Module - Secure Local Storage System
 * 
 * This module provides comprehensive secure storage functionality including:
 * - Encrypted local storage using SecureStore and MMKV
 * - SQLite database with encryption support
 * - Privacy management and data sharing controls
 * - Safe mode for offline AI processing
 * - Biometric authentication integration
 */

// Core Services
export { EncryptionService, encryptionService } from './EncryptionService';
export { SecureStorageService, secureStorageService } from './SecureStorageService';
export { DatabaseService, databaseService } from './DatabaseService';
export { PrivacyManager, privacyManager } from './PrivacyManager';
export { SafeModeService, safeModeService } from './SafeModeService';
export { KeyManager, keyManager } from './KeyManager';

// Utilities
export { default as StorageInitializer } from './StorageInitializer';

// Types and Interfaces
export type {
  EncryptionConfig,
  SecureStorageOptions,
  StorageItem,
  DatabaseSchema,
  DatabaseTable,
  DatabaseColumn,
  DatabaseIndex,
  PrivacySettings,
  DataCategory,
  SafeModeConfig,
  LocalAICapabilities,
  EncryptedData,
  StorageMetrics,
  BackupData,
  StorageEventType,
  StorageEvent,
  StorageEventListener,
} from './types';

// Error Classes
export { StorageError, EncryptionError } from './types';

// Constants
export {
  STORAGE_KEYS,
  DATA_CATEGORIES,
  DEFAULT_ENCRYPTION_CONFIG,
  DEFAULT_PRIVACY_SETTINGS,
  DEFAULT_SAFE_MODE_CONFIG,
} from './types';

/**
 * Storage Manager - Main interface for all storage operations
 */
export class StorageManager {
  private static instance: StorageManager;
  private initialized = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager();
    }
    return StorageManager.instance;
  }

  /**
   * Initialize all storage services
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      console.log('🚀 Initializing storage system...');

      // Initialize services in order
      await encryptionService.initialize();
      await secureStorageService.initialize();
      await databaseService.initialize();
      await privacyManager.initialize();
      await safeModeService.initialize();

      this.initialized = true;
      console.log('✅ Storage system initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize storage system:', error);
      throw error;
    }
  }

  /**
   * Check if storage system is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get secure storage service
   */
  getSecureStorage(): SecureStorageService {
    return secureStorageService;
  }

  /**
   * Get database service
   */
  getDatabase(): DatabaseService {
    return databaseService;
  }

  /**
   * Get privacy manager
   */
  getPrivacyManager(): PrivacyManager {
    return privacyManager;
  }

  /**
   * Get safe mode service
   */
  getSafeModeService(): SafeModeService {
    return safeModeService;
  }

  /**
   * Get encryption service
   */
  getEncryptionService(): EncryptionService {
    return encryptionService;
  }

  /**
   * Quick access methods for common operations
   */

  /**
   * Store data securely
   */
  async store<T>(key: string, value: T, encrypt: boolean = true): Promise<void> {
    await secureStorageService.setItem(key, value, { encrypt });
  }

  /**
   * Retrieve data securely
   */
  async retrieve<T>(key: string): Promise<T | null> {
    return secureStorageService.getItem<T>(key);
  }

  /**
   * Remove data
   */
  async remove(key: string): Promise<void> {
    await secureStorageService.removeItem(key);
  }

  /**
   * Check if data sharing is allowed
   */
  canShareData(dataCategory: string, forAI: boolean = false): boolean {
    return privacyManager.canShareData(dataCategory, forAI);
  }

  /**
   * Process text with local AI (safe mode)
   */
  async processWithLocalAI(
    input: string, 
    type: 'chat' | 'analyze' | 'translate' = 'chat'
  ): Promise<any> {
    return safeModeService.processText(input, type);
  }

  /**
   * Enable safe mode
   */
  async enableSafeMode(): Promise<void> {
    await privacyManager.enableSafeMode();
  }

  /**
   * Disable safe mode
   */
  async disableSafeMode(): Promise<void> {
    await privacyManager.disableSafeMode();
  }

  /**
   * Get privacy score
   */
  getPrivacyScore(): number {
    return privacyManager.getPrivacyScore();
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(settings: Partial<PrivacySettings>): Promise<void> {
    await privacyManager.updatePrivacySettings(settings);
  }

  /**
   * Export all data for backup
   */
  async exportData(): Promise<string> {
    try {
      const exportData = {
        timestamp: Date.now(),
        version: '1.0',
        privacySettings: await privacyManager.exportPrivacySettings(),
        database: await databaseService.backup(),
        metadata: {
          privacyScore: privacyManager.getPrivacyScore(),
          safeModeActive: privacyManager.isSafeModeActive(),
        },
      };

      // Encrypt the entire export
      const encrypted = await encryptionService.encryptObject(exportData);
      return JSON.stringify(encrypted);
    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }

  /**
   * Import data from backup
   */
  async importData(backupString: string): Promise<void> {
    try {
      const encrypted = JSON.parse(backupString);
      const importData = await encryptionService.decryptObject(encrypted);

      // Import privacy settings
      if (importData.privacySettings) {
        await privacyManager.importPrivacySettings(importData.privacySettings);
      }

      // Import database
      if (importData.database) {
        await databaseService.restore(importData.database);
      }

      console.log('📥 Data imported successfully');
    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }

  /**
   * Get storage statistics
   */
  async getStatistics(): Promise<any> {
    try {
      const [dbStats, safeModeStats] = await Promise.all([
        databaseService.getStats(),
        safeModeService.getStatistics(),
      ]);

      return {
        database: dbStats,
        safeMode: safeModeStats,
        privacy: {
          score: privacyManager.getPrivacyScore(),
          safeModeActive: privacyManager.isSafeModeActive(),
          isOnline: privacyManager.isNetworkAvailable(),
        },
        encryption: {
          initialized: encryptionService.isInitialized(),
          config: encryptionService.getConfig(),
        },
      };
    } catch (error) {
      console.error('Failed to get storage statistics:', error);
      throw error;
    }
  }

  /**
   * Cleanup old data and optimize storage
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Starting storage cleanup...');

      // Clear expired items
      // Note: This would be implemented based on TTL in SecureStorageService

      // Clear old cached AI responses
      await safeModeService.clearCache();

      // Optimize database
      // Note: This would be implemented in DatabaseService

      console.log('✅ Storage cleanup completed');
    } catch (error) {
      console.error('Failed to cleanup storage:', error);
      throw error;
    }
  }

  /**
   * Reset all storage (for testing or complete reset)
   */
  async reset(): Promise<void> {
    try {
      console.log('🔄 Resetting storage system...');

      await secureStorageService.clear();
      // Note: Database reset would be implemented in DatabaseService
      
      // Clear encryption keys
      encryptionService.clearMasterKey();

      this.initialized = false;
      console.log('✅ Storage system reset completed');
    } catch (error) {
      console.error('Failed to reset storage:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const storageManager = StorageManager.getInstance();

/**
 * Convenience hooks and utilities
 */

/**
 * Initialize storage system (call this in your app startup)
 */
export const initializeStorage = async (): Promise<void> => {
  await storageManager.initialize();
};

/**
 * Quick access to common storage operations
 */
export const storage = {
  store: storageManager.store.bind(storageManager),
  retrieve: storageManager.retrieve.bind(storageManager),
  remove: storageManager.remove.bind(storageManager),
  canShare: storageManager.canShareData.bind(storageManager),
  processLocal: storageManager.processWithLocalAI.bind(storageManager),
  enableSafeMode: storageManager.enableSafeMode.bind(storageManager),
  disableSafeMode: storageManager.disableSafeMode.bind(storageManager),
  getPrivacyScore: storageManager.getPrivacyScore.bind(storageManager),
  updatePrivacy: storageManager.updatePrivacySettings.bind(storageManager),
  export: storageManager.exportData.bind(storageManager),
  import: storageManager.importData.bind(storageManager),
  stats: storageManager.getStatistics.bind(storageManager),
  cleanup: storageManager.cleanup.bind(storageManager),
  reset: storageManager.reset.bind(storageManager),
};

/**
 * Default export for easy importing
 */
export default storageManager;
