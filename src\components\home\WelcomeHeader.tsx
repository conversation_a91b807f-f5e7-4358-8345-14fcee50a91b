/**
 * Welcome Header Component
 * 
 * رأس الترحيب في الشاشة الرئيسية
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslation } from 'react-i18next';

import { Text, Card } from '@/shared/components/ui';
import { useTheme } from '@/ui/theme/useTheme';

interface WelcomeHeaderProps {
  greeting: string;
  userName: string;
  currentTime: Date;
}

const WelcomeHeader: React.FC<WelcomeHeaderProps> = ({
  greeting,
  userName,
  currentTime,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Card style={styles.container} padding="none">
      <LinearGradient
        colors={[theme.colors.primary.main, theme.colors.primary.dark]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Time Display */}
          <View style={styles.timeContainer}>
            <Text
              variant="caption"
              color="primary"
              style={[styles.timeText, { color: theme.colors.primary.contrastText }]}
            >
              {formatDate(currentTime)}
            </Text>
            <Text
              variant="subtitle1"
              weight="bold"
              style={[styles.clockText, { color: theme.colors.primary.contrastText }]}
            >
              {formatTime(currentTime)}
            </Text>
          </View>

          {/* Greeting */}
          <View style={styles.greetingContainer}>
            <Text
              variant="subtitle2"
              style={[styles.greetingText, { color: theme.colors.primary.contrastText }]}
            >
              {greeting}
            </Text>
            <Text
              variant="subtitle1"
              weight="bold"
              style={[styles.nameText, { color: theme.colors.primary.contrastText }]}
            >
              {userName}
            </Text>
          </View>

          {/* Motivational Message */}
          <View style={styles.messageContainer}>
            <Text
              variant="body2"
              style={[styles.messageText, { color: theme.colors.primary.contrastText }]}
              i18nKey="home.motivationalMessage"
            />
          </View>
        </View>

        {/* Decorative Elements */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
      </LinearGradient>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 24,
    overflow: 'hidden',
  },
  gradient: {
    padding: 24,
    minHeight: 140,
    position: 'relative',
  },
  content: {
    flex: 1,
    zIndex: 2,
  },
  timeContainer: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  timeText: {
    opacity: 0.9,
    fontSize: 12,
  },
  clockText: {
    fontSize: 18,
    letterSpacing: 1,
  },
  greetingContainer: {
    marginBottom: 12,
  },
  greetingText: {
    opacity: 0.9,
    marginBottom: 2,
  },
  nameText: {
    fontSize: 20,
  },
  messageContainer: {
    marginTop: 'auto',
  },
  messageText: {
    opacity: 0.8,
    lineHeight: 20,
  },
  decorativeCircle1: {
    position: 'absolute',
    top: -30,
    right: -30,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    zIndex: 1,
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: -20,
    left: -20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    zIndex: 1,
  },
});

export default WelcomeHeader;
